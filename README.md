# 🚀 <PERSON><PERSON><PERSON> - Portfolio Website

A futuristic, immersive, and premium portfolio website showcasing modern web development skills with cutting-edge animations and 3D integration.

## ✨ Features

### 🎨 Design & Aesthetics
- **Futuristic Design**: Modern glassmorphic UI with blues, violets, and neon color scheme
- **3D Integration**: Spline 3D planet model in hero section
- **Glassmorphism**: Beautiful glass-like components with blur effects
- **Neumorphic Elements**: Soft, modern button designs
- **Responsive Design**: Fully responsive across all devices

### 🎭 Animations & Interactions
- **GSAP Loading Animation**: Cinematic preloader with progress bar
- **Locomotive Scroll**: Smooth parallax scrolling experience
- **ScrollTrigger**: Scroll-based animations throughout the site
- **Floating Elements**: Animated orbs and particles
- **Hover Effects**: Interactive elements with smooth transitions
- **Stagger Animations**: Sequential element appearances

### 🛠️ Technical Stack
- **Frontend**: Vanilla JavaScript, HTML5, CSS3
- **Build Tool**: Vite for fast development and optimized builds
- **Animation Libraries**: 
  - GSAP (GreenSock Animation Platform)
  - Locomotive Scroll for smooth scrolling
- **Icons**: Phosphor Icons (light style)
- **Typography**: Inter font family
- **3D Graphics**: Spline integration

### 📱 Responsive Features
- **Mobile Navigation**: Hamburger menu with full-screen overlay
- **Touch Optimized**: Proper touch targets for mobile devices
- **Swipeable Projects**: Mobile-friendly project carousel
- **Adaptive Layouts**: Stack layouts on smaller screens

### ♿ Accessibility
- **ARIA Labels**: Proper accessibility attributes
- **Keyboard Navigation**: Full keyboard support
- **Skip Links**: Skip to main content option
- **Reduced Motion**: Respects user motion preferences
- **High Contrast**: Support for high contrast mode
- **Screen Reader**: Semantic HTML structure

### ⚡ Performance Optimizations
- **Lazy Loading**: Images load as needed
- **Resource Preloading**: Critical resources preloaded
- **GPU Acceleration**: Hardware-accelerated animations
- **Device Optimization**: Reduced animations on low-end devices
- **Code Splitting**: Optimized bundle sizes

## 🏗️ Project Structure

```
portfolio/
├── public/
│   ├── profile-min.svg          # Profile image
│   ├── project-1-min.svg        # Project screenshots
│   ├── project-2-min.svg
│   ├── project-3-min.svg
│   ├── project-4-min.svg
│   ├── project-5-min.svg
│   ├── project-6-min.svg
│   └── vite.svg
├── src/
│   ├── main.js                  # Main JavaScript file
│   └── style.css                # All styles
├── index.html                   # Main HTML file
├── package.json                 # Dependencies
└── README.md                    # This file
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Build for production**
   ```bash
   npm run build
   ```

5. **Preview production build**
   ```bash
   npm run preview
   ```

## 📄 Sections

### 🏠 Hero Section
- Animated headline with name and title
- 3D Spline planet model background
- Floating neon orbs with parallax effect
- Call-to-action button with hover animations

### 👨‍💻 About Section
- Profile image with glow effects
- Bio and description
- Skills grid with technology icons
- Hover animations on skill items

### 💼 Projects Section
- Horizontal scrollable project cards
- Glassmorphic card design
- Hover overlays with view buttons
- Technology stack tags
- 6 featured projects showcase

### 📧 Contact Section
- Interactive contact form
- Glassmorphic input fields
- Social media links
- Form validation and animations

### 🔗 Footer
- Navigation links
- Social media icons
- Floating particle animations

## 🎯 Key Technologies Showcased

- **HTML5**: Semantic markup and accessibility
- **CSS3**: Advanced styling, animations, and responsive design
- **JavaScript**: Modern ES6+ features and DOM manipulation
- **React**: Component-based architecture (mentioned in skills)
- **Python**: Backend development capabilities
- **Java**: Enterprise application development
- **PHP & MySQL**: Database-driven applications
- **Data Analytics**: Data processing and visualization

## 🌟 Animation Details

### Loading Sequence
1. Preloader appears with animated text
2. Progress bar fills from 0% to 100%
3. Smooth transition to main content
4. Hero elements animate in sequence

### Scroll Animations
- **Fade In**: Elements appear with opacity and transform
- **Slide In**: Left/right sliding animations
- **Scale In**: Elements scale from small to normal
- **Stagger**: Sequential animations with delays

### Interactive Elements
- **Hover States**: Scale, glow, and transform effects
- **Click Animations**: Button press feedback
- **Parallax**: Different scroll speeds for depth

## 📱 Browser Support

- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **Mobile Browsers**: iOS Safari, Chrome Mobile, Samsung Internet
- **Progressive Enhancement**: Graceful degradation for older browsers

## 🔧 Customization

### Colors
Update CSS custom properties in `src/style.css`:
```css
:root {
  --primary-blue: #00d4ff;
  --accent-violet: #6366f1;
  --neon-purple: #a855f7;
  /* ... other colors */
}
```

### Content
Update personal information in `index.html`:
- Name and title in hero section
- About section bio
- Project details and images
- Contact information

### Animations
Modify GSAP animations in `src/main.js`:
- Timing and easing functions
- Animation sequences
- Scroll trigger points

## 📈 Performance Metrics

- **Lighthouse Score**: Optimized for 90+ scores
- **Core Web Vitals**: Excellent LCP, FID, and CLS scores
- **Bundle Size**: Optimized with Vite
- **Image Optimization**: SVG graphics for scalability

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 📞 Contact

**Nitesh Gupta**
- Email: <EMAIL>
- GitHub: [nitesh124-coder](https://github.com/nitesh124-coder)
- Portfolio: [Live Demo](http://localhost:5173/)

---

Built with ❤️ using modern web technologies and cutting-edge animations.
