<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Placeholder Images</title>
</head>
<body>
    <h1>Placeholder Image Generator</h1>
    <button onclick="generateImages()">Generate Placeholder Images</button>
    <div id="output"></div>

    <script>
        function generateImages() {
            const images = [
                { name: 'profile-min.png', width: 400, height: 400, text: 'Profile\nNitesh Gupta', bg: '#6366f1' },
                { name: 'project-1-min.png', width: 600, height: 400, text: 'E-Commerce\nPlatform', bg: '#00d4ff' },
                { name: 'project-2-min.png', width: 600, height: 400, text: 'Data\nVisualization', bg: '#a855f7' },
                { name: 'project-3-min.png', width: 600, height: 400, text: 'Mobile App\nUI/UX', bg: '#06b6d4' },
                { name: 'project-4-min.png', width: 600, height: 400, text: 'AI-Powered\nChatbot', bg: '#ec4899' },
                { name: 'project-5-min.png', width: 600, height: 400, text: 'Blockchain\nDApp', bg: '#0099cc' },
                { name: 'project-6-min.png', width: 600, height: 400, text: 'Real-time\nAnalytics', bg: '#8b5cf6' }
            ];

            const output = document.getElementById('output');
            output.innerHTML = '';

            images.forEach(img => {
                const canvas = document.createElement('canvas');
                canvas.width = img.width;
                canvas.height = img.height;
                const ctx = canvas.getContext('2d');

                // Create gradient background
                const gradient = ctx.createLinearGradient(0, 0, img.width, img.height);
                gradient.addColorStop(0, img.bg);
                gradient.addColorStop(1, adjustBrightness(img.bg, -30));
                
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, img.width, img.height);

                // Add text
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 32px Inter, sans-serif';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                
                const lines = img.text.split('\n');
                const lineHeight = 40;
                const startY = img.height / 2 - (lines.length - 1) * lineHeight / 2;
                
                lines.forEach((line, index) => {
                    ctx.fillText(line, img.width / 2, startY + index * lineHeight);
                });

                // Add decorative elements
                ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
                for (let i = 0; i < 5; i++) {
                    const x = Math.random() * img.width;
                    const y = Math.random() * img.height;
                    const radius = Math.random() * 20 + 10;
                    ctx.beginPath();
                    ctx.arc(x, y, radius, 0, Math.PI * 2);
                    ctx.fill();
                }

                // Create download link
                const link = document.createElement('a');
                link.download = img.name;
                link.href = canvas.toDataURL();
                link.textContent = `Download ${img.name}`;
                link.style.display = 'block';
                link.style.margin = '10px 0';
                
                output.appendChild(link);
                output.appendChild(canvas);
                output.appendChild(document.createElement('br'));
            });
        }

        function adjustBrightness(hex, percent) {
            const num = parseInt(hex.replace("#", ""), 16);
            const amt = Math.round(2.55 * percent);
            const R = (num >> 16) + amt;
            const G = (num >> 8 & 0x00FF) + amt;
            const B = (num & 0x0000FF) + amt;
            return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
        }
    </script>
</body>
</html>
