<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON> - Web Developer | Modern Digital Experiences</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="<PERSON><PERSON><PERSON> - Passionate web developer creating immersive digital experiences with cutting-edge technology. Specializing in React, Python, Java, and modern web development.">
    <meta name="keywords" content="web developer, frontend developer, React, Python, Java, JavaScript, portfolio, modern web design">
    <meta name="author" content="<PERSON><PERSON><PERSON>">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Ni<PERSON><PERSON> - Web Developer">
    <meta property="og:description" content="Creating digital experiences with cutting-edge technology">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://niteshgupta.dev">
    <meta property="og:image" content="/profile-min.svg">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Nitesh Gupta - Web Developer">
    <meta name="twitter:description" content="Creating digital experiences with cutting-edge technology">
    <meta name="twitter:image" content="/profile-min.svg">

    <!-- Performance and Security -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="theme-color" content="#6366f1">
    <link rel="dns-prefetch" href="https://fonts.googleapis.com">
    <link rel="dns-prefetch" href="https://fonts.gstatic.com">
    <link rel="dns-prefetch" href="https://unpkg.com">
    <link rel="dns-prefetch" href="https://my.spline.design">

    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/phosphor-icons"></script>
  </head>
  <body>
    <!-- Skip to main content for accessibility -->
    <a href="#main" class="skip-link">Skip to main content</a>

    <!-- Preloader -->
    <div class="preloader" id="preloader">
      <div class="preloader-content">
        <div class="preloader-logo">
          <h1 class="preloader-text">hello world</h1>
        </div>
        <div class="progress-container">
          <div class="progress-bar" id="progressBar"></div>
          <div class="progress-text" id="progressText">0%</div>
        </div>
      </div>
      <div class="preloader-bg"></div>
    </div>

    <!-- Navigation -->
    <nav class="nav" id="nav">
      <div class="nav-container">
        <div class="nav-logo">
          <a href="#home">NG</a>
        </div>
        <div class="nav-menu" id="navMenu">
          <a href="#home" class="nav-link">Home</a>
          <a href="#about" class="nav-link">About</a>
          <a href="#projects" class="nav-link">Projects</a>
          <a href="#contact" class="nav-link">Contact</a>
        </div>
        <button class="nav-hamburger" id="navHamburger" aria-label="Toggle navigation menu" aria-expanded="false">
          <span></span>
          <span></span>
          <span></span>
        </button>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="main" id="main" data-scroll-container>
      <!-- Hero Section -->
      <section class="hero" id="home" data-scroll-section>
        <div class="hero-bg">
          <!-- Spline 3D Model -->
          <div class="hero-spline" data-scroll data-scroll-speed="0.5">
            <iframe src='https://my.spline.design/worldplanet-wHfKolcVJtVVCAXIoLc2iVqs/' frameborder='0' width='100%' height='100%' title="3D Planet Animation" aria-label="Interactive 3D planet model"></iframe>
          </div>

          <!-- Floating Orbs -->
          <div class="floating-orbs">
            <div class="orb orb-1" data-scroll data-scroll-speed="0.2"></div>
            <div class="orb orb-2" data-scroll data-scroll-speed="-0.3"></div>
            <div class="orb orb-3" data-scroll data-scroll-speed="0.4"></div>
          </div>
        </div>

        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title" data-scroll data-scroll-speed="0.1">
              Hi, I'm <span class="highlight">Nitesh Gupta</span><br>
              Web Developer
            </h1>
            <p class="hero-subtitle" data-scroll data-scroll-speed="0.2">
              Creating digital experiences with cutting-edge technology
            </p>
            <button class="cta-button" id="ctaButton" data-scroll data-scroll-speed="0.3">
              <span>Hire Me</span>
              <i class="ph-arrow-right"></i>
            </button>
          </div>
        </div>
      </section>

      <!-- About Section -->
      <section class="about" id="about" data-scroll-section>
        <div class="container">
          <div class="about-content">
            <div class="about-image" data-scroll data-scroll-speed="0.2">
              <div class="image-container">
                <img src="/profile-min.svg" alt="Nitesh Gupta" class="profile-img">
                <div class="image-glow"></div>
              </div>
            </div>

            <div class="about-text" data-scroll data-scroll-speed="0.1">
              <h2 class="section-title">About Me</h2>
              <p class="about-description">
                I'm a passionate web developer with expertise in modern technologies.
                I create immersive digital experiences that combine beautiful design
                with powerful functionality.
              </p>

              <div class="skills-grid">
                <div class="skill-item" data-skill="html">
                  <i class="ph-file-html"></i>
                  <span>HTML</span>
                </div>
                <div class="skill-item" data-skill="css">
                  <i class="ph-file-css"></i>
                  <span>CSS</span>
                </div>
                <div class="skill-item" data-skill="js">
                  <i class="ph-file-js"></i>
                  <span>JavaScript</span>
                </div>
                <div class="skill-item" data-skill="react">
                  <i class="ph-atom"></i>
                  <span>React</span>
                </div>
                <div class="skill-item" data-skill="python">
                  <i class="ph-file-py"></i>
                  <span>Python</span>
                </div>
                <div class="skill-item" data-skill="java">
                  <i class="ph-coffee"></i>
                  <span>Java</span>
                </div>
                <div class="skill-item" data-skill="vibe">
                  <i class="ph-music-note"></i>
                  <span>Vibe Coding</span>
                </div>
                <div class="skill-item" data-skill="php">
                  <i class="ph-file-php"></i>
                  <span>PHP</span>
                </div>
                <div class="skill-item" data-skill="mysql">
                  <i class="ph-database"></i>
                  <span>MySQL</span>
                </div>
                <div class="skill-item" data-skill="analytics">
                  <i class="ph-chart-bar"></i>
                  <span>Data Analytics</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Projects Section -->
      <section class="projects" id="projects" data-scroll-section>
        <div class="container">
          <h2 class="section-title" data-scroll data-scroll-speed="0.1">Featured Projects</h2>

          <div class="projects-container" id="projectsContainer" data-scroll data-scroll-speed="0.2">
            <div class="project-card" data-project="1">
              <div class="project-image">
                <img src="/project-1-min.svg" alt="Project 1">
                <div class="project-overlay">
                  <button class="project-btn">View Project</button>
                </div>
              </div>
              <div class="project-info">
                <h3 class="project-title">E-Commerce Platform</h3>
                <p class="project-description">Modern e-commerce solution with advanced features</p>
                <div class="project-tech">
                  <span class="tech-tag">React</span>
                  <span class="tech-tag">Node.js</span>
                  <span class="tech-tag">MongoDB</span>
                </div>
              </div>
            </div>

            <div class="project-card" data-project="2">
              <div class="project-image">
                <img src="/project-2-min.svg" alt="Project 2">
                <div class="project-overlay">
                  <button class="project-btn">View Project</button>
                </div>
              </div>
              <div class="project-info">
                <h3 class="project-title">Data Visualization Dashboard</h3>
                <p class="project-description">Interactive dashboard for data analytics</p>
                <div class="project-tech">
                  <span class="tech-tag">Python</span>
                  <span class="tech-tag">D3.js</span>
                  <span class="tech-tag">Flask</span>
                </div>
              </div>
            </div>

            <div class="project-card" data-project="3">
              <div class="project-image">
                <img src="/project-3-min.svg" alt="Project 3">
                <div class="project-overlay">
                  <button class="project-btn">View Project</button>
                </div>
              </div>
              <div class="project-info">
                <h3 class="project-title">Mobile App UI/UX</h3>
                <p class="project-description">Beautiful mobile application interface</p>
                <div class="project-tech">
                  <span class="tech-tag">React Native</span>
                  <span class="tech-tag">Figma</span>
                  <span class="tech-tag">Firebase</span>
                </div>
              </div>
            </div>

            <div class="project-card" data-project="4">
              <div class="project-image">
                <img src="/project-4-min.svg" alt="Project 4">
                <div class="project-overlay">
                  <button class="project-btn">View Project</button>
                </div>
              </div>
              <div class="project-info">
                <h3 class="project-title">AI-Powered Chatbot</h3>
                <p class="project-description">Intelligent chatbot with natural language processing</p>
                <div class="project-tech">
                  <span class="tech-tag">Python</span>
                  <span class="tech-tag">TensorFlow</span>
                  <span class="tech-tag">API</span>
                </div>
              </div>
            </div>

            <div class="project-card" data-project="5">
              <div class="project-image">
                <img src="/project-5-min.svg" alt="Project 5">
                <div class="project-overlay">
                  <button class="project-btn">View Project</button>
                </div>
              </div>
              <div class="project-info">
                <h3 class="project-title">Blockchain DApp</h3>
                <p class="project-description">Decentralized application on blockchain</p>
                <div class="project-tech">
                  <span class="tech-tag">Solidity</span>
                  <span class="tech-tag">Web3.js</span>
                  <span class="tech-tag">Ethereum</span>
                </div>
              </div>
            </div>

            <div class="project-card" data-project="6">
              <div class="project-image">
                <img src="/project-6-min.svg" alt="Project 6">
                <div class="project-overlay">
                  <button class="project-btn">View Project</button>
                </div>
              </div>
              <div class="project-info">
                <h3 class="project-title">Real-time Analytics</h3>
                <p class="project-description">Live data processing and visualization</p>
                <div class="project-tech">
                  <span class="tech-tag">Java</span>
                  <span class="tech-tag">Apache Kafka</span>
                  <span class="tech-tag">Redis</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Contact Section -->
      <section class="contact" id="contact" data-scroll-section>
        <div class="container">
          <h2 class="section-title" data-scroll data-scroll-speed="0.1">Get In Touch</h2>

          <div class="contact-content" data-scroll data-scroll-speed="0.2">
            <div class="contact-form-container">
              <form class="contact-form" id="contactForm">
                <div class="form-group">
                  <input type="text" id="name" name="name" placeholder="Your Name" required>
                  <label for="name">Name</label>
                </div>

                <div class="form-group">
                  <input type="email" id="email" name="email" placeholder="Your Email" required>
                  <label for="email">Email</label>
                </div>

                <div class="form-group">
                  <textarea id="message" name="message" placeholder="Your Message" rows="5" required></textarea>
                  <label for="message">Message</label>
                </div>

                <button type="submit" class="submit-btn" id="submitBtn">
                  <span>Send Message</span>
                  <i class="ph-paper-plane-tilt"></i>
                </button>
              </form>
            </div>

            <div class="contact-info">
              <div class="contact-item">
                <i class="ph-envelope"></i>
                <span><EMAIL></span>
              </div>

              <div class="social-links">
                <a href="https://github.com/nitesh124-coder" target="_blank" class="social-link">
                  <i class="ph-github-logo"></i>
                </a>
                <a href="#" target="_blank" class="social-link">
                  <i class="ph-linkedin-logo"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer -->
    <footer class="footer" data-scroll-section>
      <div class="container">
        <div class="footer-content" data-scroll data-scroll-speed="0.1">
          <div class="footer-links">
            <a href="#home">Home</a>
            <a href="#about">About</a>
            <a href="#projects">Projects</a>
            <a href="#contact">Contact</a>
          </div>

          <div class="footer-social">
            <a href="https://github.com/nitesh124-coder" target="_blank">
              <i class="ph-github-logo"></i>
            </a>
            <a href="#" target="_blank">
              <i class="ph-linkedin-logo"></i>
            </a>
          </div>
        </div>

        <div class="footer-particles">
          <div class="particle particle-1"></div>
          <div class="particle particle-2"></div>
          <div class="particle particle-3"></div>
          <div class="particle particle-4"></div>
          <div class="particle particle-5"></div>
        </div>
      </div>
    </footer>

    <!-- Background Elements -->
    <div class="bg-elements">
      <div class="bg-orb bg-orb-1"></div>
      <div class="bg-orb bg-orb-2"></div>
      <div class="bg-orb bg-orb-3"></div>
    </div>

    <script type="module" src="/src/main.js"></script>
  </body>
</html>
