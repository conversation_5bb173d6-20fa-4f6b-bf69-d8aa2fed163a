{"version": 3, "sources": ["../../locomotive-scroll/dist/locomotive-scroll.esm.js"], "sourcesContent": ["/* locomotive-scroll v4.1.3 | MIT License | https://github.com/locomotivemtl/locomotive-scroll */\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n\n  try {\n    Date.prototype.toString.call(Reflect.construct(Date, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n        result;\n\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n\n    return _possibleConstructorReturn(this, result);\n  };\n}\n\nfunction _superPropBase(object, property) {\n  while (!Object.prototype.hasOwnProperty.call(object, property)) {\n    object = _getPrototypeOf(object);\n    if (object === null) break;\n  }\n\n  return object;\n}\n\nfunction _get(target, property, receiver) {\n  if (typeof Reflect !== \"undefined\" && Reflect.get) {\n    _get = Reflect.get;\n  } else {\n    _get = function _get(target, property, receiver) {\n      var base = _superPropBase(target, property);\n\n      if (!base) return;\n      var desc = Object.getOwnPropertyDescriptor(base, property);\n\n      if (desc.get) {\n        return desc.get.call(receiver);\n      }\n\n      return desc.value;\n    };\n  }\n\n  return _get(target, property, receiver || target);\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter);\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar defaults = {\n  el: document,\n  name: 'scroll',\n  offset: [0, 0],\n  repeat: false,\n  smooth: false,\n  initPosition: {\n    x: 0,\n    y: 0\n  },\n  direction: 'vertical',\n  gestureDirection: 'vertical',\n  reloadOnContextChange: false,\n  lerp: 0.1,\n  \"class\": 'is-inview',\n  scrollbarContainer: false,\n  scrollbarClass: 'c-scrollbar',\n  scrollingClass: 'has-scroll-scrolling',\n  draggingClass: 'has-scroll-dragging',\n  smoothClass: 'has-scroll-smooth',\n  initClass: 'has-scroll-init',\n  getSpeed: false,\n  getDirection: false,\n  scrollFromAnywhere: false,\n  multiplier: 1,\n  firefoxMultiplier: 50,\n  touchMultiplier: 2,\n  resetNativeScroll: true,\n  tablet: {\n    smooth: false,\n    direction: 'vertical',\n    gestureDirection: 'vertical',\n    breakpoint: 1024\n  },\n  smartphone: {\n    smooth: false,\n    direction: 'vertical',\n    gestureDirection: 'vertical'\n  }\n};\n\nvar _default = /*#__PURE__*/function () {\n  function _default() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n    _classCallCheck(this, _default);\n\n    Object.assign(this, defaults, options);\n    this.smartphone = defaults.smartphone;\n    if (options.smartphone) Object.assign(this.smartphone, options.smartphone);\n    this.tablet = defaults.tablet;\n    if (options.tablet) Object.assign(this.tablet, options.tablet);\n    this.namespace = 'locomotive';\n    this.html = document.documentElement;\n    this.windowHeight = window.innerHeight;\n    this.windowWidth = window.innerWidth;\n    this.windowMiddle = {\n      x: this.windowWidth / 2,\n      y: this.windowHeight / 2\n    };\n    this.els = {};\n    this.currentElements = {};\n    this.listeners = {};\n    this.hasScrollTicking = false;\n    this.hasCallEventSet = false;\n    this.checkScroll = this.checkScroll.bind(this);\n    this.checkResize = this.checkResize.bind(this);\n    this.checkEvent = this.checkEvent.bind(this);\n    this.instance = {\n      scroll: {\n        x: 0,\n        y: 0\n      },\n      limit: {\n        x: this.html.offsetWidth,\n        y: this.html.offsetHeight\n      },\n      currentElements: this.currentElements\n    };\n\n    if (this.isMobile) {\n      if (this.isTablet) {\n        this.context = 'tablet';\n      } else {\n        this.context = 'smartphone';\n      }\n    } else {\n      this.context = 'desktop';\n    }\n\n    if (this.isMobile) this.direction = this[this.context].direction;\n\n    if (this.direction === 'horizontal') {\n      this.directionAxis = 'x';\n    } else {\n      this.directionAxis = 'y';\n    }\n\n    if (this.getDirection) {\n      this.instance.direction = null;\n    }\n\n    if (this.getDirection) {\n      this.instance.speed = 0;\n    }\n\n    this.html.classList.add(this.initClass);\n    window.addEventListener('resize', this.checkResize, false);\n  }\n\n  _createClass(_default, [{\n    key: \"init\",\n    value: function init() {\n      this.initEvents();\n    }\n  }, {\n    key: \"checkScroll\",\n    value: function checkScroll() {\n      this.dispatchScroll();\n    }\n  }, {\n    key: \"checkResize\",\n    value: function checkResize() {\n      var _this = this;\n\n      if (!this.resizeTick) {\n        this.resizeTick = true;\n        requestAnimationFrame(function () {\n          _this.resize();\n\n          _this.resizeTick = false;\n        });\n      }\n    }\n  }, {\n    key: \"resize\",\n    value: function resize() {}\n  }, {\n    key: \"checkContext\",\n    value: function checkContext() {\n      if (!this.reloadOnContextChange) return;\n      this.isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1 || this.windowWidth < this.tablet.breakpoint;\n      this.isTablet = this.isMobile && this.windowWidth >= this.tablet.breakpoint;\n      var oldContext = this.context;\n\n      if (this.isMobile) {\n        if (this.isTablet) {\n          this.context = 'tablet';\n        } else {\n          this.context = 'smartphone';\n        }\n      } else {\n        this.context = 'desktop';\n      }\n\n      if (oldContext != this.context) {\n        var oldSmooth = oldContext == 'desktop' ? this.smooth : this[oldContext].smooth;\n        var newSmooth = this.context == 'desktop' ? this.smooth : this[this.context].smooth;\n        if (oldSmooth != newSmooth) window.location.reload();\n      }\n    }\n  }, {\n    key: \"initEvents\",\n    value: function initEvents() {\n      var _this2 = this;\n\n      this.scrollToEls = this.el.querySelectorAll(\"[data-\".concat(this.name, \"-to]\"));\n      this.setScrollTo = this.setScrollTo.bind(this);\n      this.scrollToEls.forEach(function (el) {\n        el.addEventListener('click', _this2.setScrollTo, false);\n      });\n    }\n  }, {\n    key: \"setScrollTo\",\n    value: function setScrollTo(event) {\n      event.preventDefault();\n      this.scrollTo(event.currentTarget.getAttribute(\"data-\".concat(this.name, \"-href\")) || event.currentTarget.getAttribute('href'), {\n        offset: event.currentTarget.getAttribute(\"data-\".concat(this.name, \"-offset\"))\n      });\n    }\n  }, {\n    key: \"addElements\",\n    value: function addElements() {}\n  }, {\n    key: \"detectElements\",\n    value: function detectElements(hasCallEventSet) {\n      var _this3 = this;\n\n      var scrollTop = this.instance.scroll.y;\n      var scrollBottom = scrollTop + this.windowHeight;\n      var scrollLeft = this.instance.scroll.x;\n      var scrollRight = scrollLeft + this.windowWidth;\n      Object.entries(this.els).forEach(function (_ref) {\n        var _ref2 = _slicedToArray(_ref, 2),\n            i = _ref2[0],\n            el = _ref2[1];\n\n        if (el && (!el.inView || hasCallEventSet)) {\n          if (_this3.direction === 'horizontal') {\n            if (scrollRight >= el.left && scrollLeft < el.right) {\n              _this3.setInView(el, i);\n            }\n          } else {\n            if (scrollBottom >= el.top && scrollTop < el.bottom) {\n              _this3.setInView(el, i);\n            }\n          }\n        }\n\n        if (el && el.inView) {\n          if (_this3.direction === 'horizontal') {\n            var width = el.right - el.left;\n            el.progress = (_this3.instance.scroll.x - (el.left - _this3.windowWidth)) / (width + _this3.windowWidth);\n\n            if (scrollRight < el.left || scrollLeft > el.right) {\n              _this3.setOutOfView(el, i);\n            }\n          } else {\n            var height = el.bottom - el.top;\n            el.progress = (_this3.instance.scroll.y - (el.top - _this3.windowHeight)) / (height + _this3.windowHeight);\n\n            if (scrollBottom < el.top || scrollTop > el.bottom) {\n              _this3.setOutOfView(el, i);\n            }\n          }\n        }\n      }); // this.els = this.els.filter((current, i) => {\n      //     return current !== null;\n      // });\n\n      this.hasScrollTicking = false;\n    }\n  }, {\n    key: \"setInView\",\n    value: function setInView(current, i) {\n      this.els[i].inView = true;\n      current.el.classList.add(current[\"class\"]);\n      this.currentElements[i] = current;\n\n      if (current.call && this.hasCallEventSet) {\n        this.dispatchCall(current, 'enter');\n\n        if (!current.repeat) {\n          this.els[i].call = false;\n        }\n      } // if (!current.repeat && !current.speed && !current.sticky) {\n      //     if (!current.call || current.call && this.hasCallEventSet) {\n      //        this.els[i] = null\n      //     }\n      // }\n\n    }\n  }, {\n    key: \"setOutOfView\",\n    value: function setOutOfView(current, i) {\n      var _this4 = this;\n\n      // if (current.repeat || current.speed !== undefined) {\n      this.els[i].inView = false; // }\n\n      Object.keys(this.currentElements).forEach(function (el) {\n        el === i && delete _this4.currentElements[el];\n      });\n\n      if (current.call && this.hasCallEventSet) {\n        this.dispatchCall(current, 'exit');\n      }\n\n      if (current.repeat) {\n        current.el.classList.remove(current[\"class\"]);\n      }\n    }\n  }, {\n    key: \"dispatchCall\",\n    value: function dispatchCall(current, way) {\n      this.callWay = way;\n      this.callValue = current.call.split(',').map(function (item) {\n        return item.trim();\n      });\n      this.callObj = current;\n      if (this.callValue.length == 1) this.callValue = this.callValue[0];\n      var callEvent = new Event(this.namespace + 'call');\n      this.el.dispatchEvent(callEvent);\n    }\n  }, {\n    key: \"dispatchScroll\",\n    value: function dispatchScroll() {\n      var scrollEvent = new Event(this.namespace + 'scroll');\n      this.el.dispatchEvent(scrollEvent);\n    }\n  }, {\n    key: \"setEvents\",\n    value: function setEvents(event, func) {\n      if (!this.listeners[event]) {\n        this.listeners[event] = [];\n      }\n\n      var list = this.listeners[event];\n      list.push(func);\n\n      if (list.length === 1) {\n        this.el.addEventListener(this.namespace + event, this.checkEvent, false);\n      }\n\n      if (event === 'call') {\n        this.hasCallEventSet = true;\n        this.detectElements(true);\n      }\n    }\n  }, {\n    key: \"unsetEvents\",\n    value: function unsetEvents(event, func) {\n      if (!this.listeners[event]) return;\n      var list = this.listeners[event];\n      var index = list.indexOf(func);\n      if (index < 0) return;\n      list.splice(index, 1);\n\n      if (list.index === 0) {\n        this.el.removeEventListener(this.namespace + event, this.checkEvent, false);\n      }\n    }\n  }, {\n    key: \"checkEvent\",\n    value: function checkEvent(event) {\n      var _this5 = this;\n\n      var name = event.type.replace(this.namespace, '');\n      var list = this.listeners[name];\n      if (!list || list.length === 0) return;\n      list.forEach(function (func) {\n        switch (name) {\n          case 'scroll':\n            return func(_this5.instance);\n\n          case 'call':\n            return func(_this5.callValue, _this5.callWay, _this5.callObj);\n\n          default:\n            return func();\n        }\n      });\n    }\n  }, {\n    key: \"startScroll\",\n    value: function startScroll() {}\n  }, {\n    key: \"stopScroll\",\n    value: function stopScroll() {}\n  }, {\n    key: \"setScroll\",\n    value: function setScroll(x, y) {\n      this.instance.scroll = {\n        x: 0,\n        y: 0\n      };\n    }\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      var _this6 = this;\n\n      window.removeEventListener('resize', this.checkResize, false);\n      Object.keys(this.listeners).forEach(function (event) {\n        _this6.el.removeEventListener(_this6.namespace + event, _this6.checkEvent, false);\n      });\n      this.listeners = {};\n      this.scrollToEls.forEach(function (el) {\n        el.removeEventListener('click', _this6.setScrollTo, false);\n      });\n      this.html.classList.remove(this.initClass);\n    }\n  }]);\n\n  return _default;\n}();\n\nvar commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n\nfunction createCommonjsModule(fn, module) {\n\treturn module = { exports: {} }, fn(module, module.exports), module.exports;\n}\n\nvar smoothscroll = createCommonjsModule(function (module, exports) {\n/* smoothscroll v0.4.4 - 2019 - Dustan Kasten, Jeremias Menichelli - MIT License */\n(function () {\n\n  // polyfill\n  function polyfill() {\n    // aliases\n    var w = window;\n    var d = document;\n\n    // return if scroll behavior is supported and polyfill is not forced\n    if (\n      'scrollBehavior' in d.documentElement.style &&\n      w.__forceSmoothScrollPolyfill__ !== true\n    ) {\n      return;\n    }\n\n    // globals\n    var Element = w.HTMLElement || w.Element;\n    var SCROLL_TIME = 468;\n\n    // object gathering original scroll methods\n    var original = {\n      scroll: w.scroll || w.scrollTo,\n      scrollBy: w.scrollBy,\n      elementScroll: Element.prototype.scroll || scrollElement,\n      scrollIntoView: Element.prototype.scrollIntoView\n    };\n\n    // define timing method\n    var now =\n      w.performance && w.performance.now\n        ? w.performance.now.bind(w.performance)\n        : Date.now;\n\n    /**\n     * indicates if a the current browser is made by Microsoft\n     * @method isMicrosoftBrowser\n     * @param {String} userAgent\n     * @returns {Boolean}\n     */\n    function isMicrosoftBrowser(userAgent) {\n      var userAgentPatterns = ['MSIE ', 'Trident/', 'Edge/'];\n\n      return new RegExp(userAgentPatterns.join('|')).test(userAgent);\n    }\n\n    /*\n     * IE has rounding bug rounding down clientHeight and clientWidth and\n     * rounding up scrollHeight and scrollWidth causing false positives\n     * on hasScrollableSpace\n     */\n    var ROUNDING_TOLERANCE = isMicrosoftBrowser(w.navigator.userAgent) ? 1 : 0;\n\n    /**\n     * changes scroll position inside an element\n     * @method scrollElement\n     * @param {Number} x\n     * @param {Number} y\n     * @returns {undefined}\n     */\n    function scrollElement(x, y) {\n      this.scrollLeft = x;\n      this.scrollTop = y;\n    }\n\n    /**\n     * returns result of applying ease math function to a number\n     * @method ease\n     * @param {Number} k\n     * @returns {Number}\n     */\n    function ease(k) {\n      return 0.5 * (1 - Math.cos(Math.PI * k));\n    }\n\n    /**\n     * indicates if a smooth behavior should be applied\n     * @method shouldBailOut\n     * @param {Number|Object} firstArg\n     * @returns {Boolean}\n     */\n    function shouldBailOut(firstArg) {\n      if (\n        firstArg === null ||\n        typeof firstArg !== 'object' ||\n        firstArg.behavior === undefined ||\n        firstArg.behavior === 'auto' ||\n        firstArg.behavior === 'instant'\n      ) {\n        // first argument is not an object/null\n        // or behavior is auto, instant or undefined\n        return true;\n      }\n\n      if (typeof firstArg === 'object' && firstArg.behavior === 'smooth') {\n        // first argument is an object and behavior is smooth\n        return false;\n      }\n\n      // throw error when behavior is not supported\n      throw new TypeError(\n        'behavior member of ScrollOptions ' +\n          firstArg.behavior +\n          ' is not a valid value for enumeration ScrollBehavior.'\n      );\n    }\n\n    /**\n     * indicates if an element has scrollable space in the provided axis\n     * @method hasScrollableSpace\n     * @param {Node} el\n     * @param {String} axis\n     * @returns {Boolean}\n     */\n    function hasScrollableSpace(el, axis) {\n      if (axis === 'Y') {\n        return el.clientHeight + ROUNDING_TOLERANCE < el.scrollHeight;\n      }\n\n      if (axis === 'X') {\n        return el.clientWidth + ROUNDING_TOLERANCE < el.scrollWidth;\n      }\n    }\n\n    /**\n     * indicates if an element has a scrollable overflow property in the axis\n     * @method canOverflow\n     * @param {Node} el\n     * @param {String} axis\n     * @returns {Boolean}\n     */\n    function canOverflow(el, axis) {\n      var overflowValue = w.getComputedStyle(el, null)['overflow' + axis];\n\n      return overflowValue === 'auto' || overflowValue === 'scroll';\n    }\n\n    /**\n     * indicates if an element can be scrolled in either axis\n     * @method isScrollable\n     * @param {Node} el\n     * @param {String} axis\n     * @returns {Boolean}\n     */\n    function isScrollable(el) {\n      var isScrollableY = hasScrollableSpace(el, 'Y') && canOverflow(el, 'Y');\n      var isScrollableX = hasScrollableSpace(el, 'X') && canOverflow(el, 'X');\n\n      return isScrollableY || isScrollableX;\n    }\n\n    /**\n     * finds scrollable parent of an element\n     * @method findScrollableParent\n     * @param {Node} el\n     * @returns {Node} el\n     */\n    function findScrollableParent(el) {\n      while (el !== d.body && isScrollable(el) === false) {\n        el = el.parentNode || el.host;\n      }\n\n      return el;\n    }\n\n    /**\n     * self invoked function that, given a context, steps through scrolling\n     * @method step\n     * @param {Object} context\n     * @returns {undefined}\n     */\n    function step(context) {\n      var time = now();\n      var value;\n      var currentX;\n      var currentY;\n      var elapsed = (time - context.startTime) / SCROLL_TIME;\n\n      // avoid elapsed times higher than one\n      elapsed = elapsed > 1 ? 1 : elapsed;\n\n      // apply easing to elapsed time\n      value = ease(elapsed);\n\n      currentX = context.startX + (context.x - context.startX) * value;\n      currentY = context.startY + (context.y - context.startY) * value;\n\n      context.method.call(context.scrollable, currentX, currentY);\n\n      // scroll more if we have not reached our destination\n      if (currentX !== context.x || currentY !== context.y) {\n        w.requestAnimationFrame(step.bind(w, context));\n      }\n    }\n\n    /**\n     * scrolls window or element with a smooth behavior\n     * @method smoothScroll\n     * @param {Object|Node} el\n     * @param {Number} x\n     * @param {Number} y\n     * @returns {undefined}\n     */\n    function smoothScroll(el, x, y) {\n      var scrollable;\n      var startX;\n      var startY;\n      var method;\n      var startTime = now();\n\n      // define scroll context\n      if (el === d.body) {\n        scrollable = w;\n        startX = w.scrollX || w.pageXOffset;\n        startY = w.scrollY || w.pageYOffset;\n        method = original.scroll;\n      } else {\n        scrollable = el;\n        startX = el.scrollLeft;\n        startY = el.scrollTop;\n        method = scrollElement;\n      }\n\n      // scroll looping over a frame\n      step({\n        scrollable: scrollable,\n        method: method,\n        startTime: startTime,\n        startX: startX,\n        startY: startY,\n        x: x,\n        y: y\n      });\n    }\n\n    // ORIGINAL METHODS OVERRIDES\n    // w.scroll and w.scrollTo\n    w.scroll = w.scrollTo = function() {\n      // avoid action when no arguments are passed\n      if (arguments[0] === undefined) {\n        return;\n      }\n\n      // avoid smooth behavior if not required\n      if (shouldBailOut(arguments[0]) === true) {\n        original.scroll.call(\n          w,\n          arguments[0].left !== undefined\n            ? arguments[0].left\n            : typeof arguments[0] !== 'object'\n              ? arguments[0]\n              : w.scrollX || w.pageXOffset,\n          // use top prop, second argument if present or fallback to scrollY\n          arguments[0].top !== undefined\n            ? arguments[0].top\n            : arguments[1] !== undefined\n              ? arguments[1]\n              : w.scrollY || w.pageYOffset\n        );\n\n        return;\n      }\n\n      // LET THE SMOOTHNESS BEGIN!\n      smoothScroll.call(\n        w,\n        d.body,\n        arguments[0].left !== undefined\n          ? ~~arguments[0].left\n          : w.scrollX || w.pageXOffset,\n        arguments[0].top !== undefined\n          ? ~~arguments[0].top\n          : w.scrollY || w.pageYOffset\n      );\n    };\n\n    // w.scrollBy\n    w.scrollBy = function() {\n      // avoid action when no arguments are passed\n      if (arguments[0] === undefined) {\n        return;\n      }\n\n      // avoid smooth behavior if not required\n      if (shouldBailOut(arguments[0])) {\n        original.scrollBy.call(\n          w,\n          arguments[0].left !== undefined\n            ? arguments[0].left\n            : typeof arguments[0] !== 'object' ? arguments[0] : 0,\n          arguments[0].top !== undefined\n            ? arguments[0].top\n            : arguments[1] !== undefined ? arguments[1] : 0\n        );\n\n        return;\n      }\n\n      // LET THE SMOOTHNESS BEGIN!\n      smoothScroll.call(\n        w,\n        d.body,\n        ~~arguments[0].left + (w.scrollX || w.pageXOffset),\n        ~~arguments[0].top + (w.scrollY || w.pageYOffset)\n      );\n    };\n\n    // Element.prototype.scroll and Element.prototype.scrollTo\n    Element.prototype.scroll = Element.prototype.scrollTo = function() {\n      // avoid action when no arguments are passed\n      if (arguments[0] === undefined) {\n        return;\n      }\n\n      // avoid smooth behavior if not required\n      if (shouldBailOut(arguments[0]) === true) {\n        // if one number is passed, throw error to match Firefox implementation\n        if (typeof arguments[0] === 'number' && arguments[1] === undefined) {\n          throw new SyntaxError('Value could not be converted');\n        }\n\n        original.elementScroll.call(\n          this,\n          // use left prop, first number argument or fallback to scrollLeft\n          arguments[0].left !== undefined\n            ? ~~arguments[0].left\n            : typeof arguments[0] !== 'object' ? ~~arguments[0] : this.scrollLeft,\n          // use top prop, second argument or fallback to scrollTop\n          arguments[0].top !== undefined\n            ? ~~arguments[0].top\n            : arguments[1] !== undefined ? ~~arguments[1] : this.scrollTop\n        );\n\n        return;\n      }\n\n      var left = arguments[0].left;\n      var top = arguments[0].top;\n\n      // LET THE SMOOTHNESS BEGIN!\n      smoothScroll.call(\n        this,\n        this,\n        typeof left === 'undefined' ? this.scrollLeft : ~~left,\n        typeof top === 'undefined' ? this.scrollTop : ~~top\n      );\n    };\n\n    // Element.prototype.scrollBy\n    Element.prototype.scrollBy = function() {\n      // avoid action when no arguments are passed\n      if (arguments[0] === undefined) {\n        return;\n      }\n\n      // avoid smooth behavior if not required\n      if (shouldBailOut(arguments[0]) === true) {\n        original.elementScroll.call(\n          this,\n          arguments[0].left !== undefined\n            ? ~~arguments[0].left + this.scrollLeft\n            : ~~arguments[0] + this.scrollLeft,\n          arguments[0].top !== undefined\n            ? ~~arguments[0].top + this.scrollTop\n            : ~~arguments[1] + this.scrollTop\n        );\n\n        return;\n      }\n\n      this.scroll({\n        left: ~~arguments[0].left + this.scrollLeft,\n        top: ~~arguments[0].top + this.scrollTop,\n        behavior: arguments[0].behavior\n      });\n    };\n\n    // Element.prototype.scrollIntoView\n    Element.prototype.scrollIntoView = function() {\n      // avoid smooth behavior if not required\n      if (shouldBailOut(arguments[0]) === true) {\n        original.scrollIntoView.call(\n          this,\n          arguments[0] === undefined ? true : arguments[0]\n        );\n\n        return;\n      }\n\n      // LET THE SMOOTHNESS BEGIN!\n      var scrollableParent = findScrollableParent(this);\n      var parentRects = scrollableParent.getBoundingClientRect();\n      var clientRects = this.getBoundingClientRect();\n\n      if (scrollableParent !== d.body) {\n        // reveal element inside parent\n        smoothScroll.call(\n          this,\n          scrollableParent,\n          scrollableParent.scrollLeft + clientRects.left - parentRects.left,\n          scrollableParent.scrollTop + clientRects.top - parentRects.top\n        );\n\n        // reveal parent in viewport unless is fixed\n        if (w.getComputedStyle(scrollableParent).position !== 'fixed') {\n          w.scrollBy({\n            left: parentRects.left,\n            top: parentRects.top,\n            behavior: 'smooth'\n          });\n        }\n      } else {\n        // reveal element in viewport\n        w.scrollBy({\n          left: clientRects.left,\n          top: clientRects.top,\n          behavior: 'smooth'\n        });\n      }\n    };\n  }\n\n  {\n    // commonjs\n    module.exports = { polyfill: polyfill };\n  }\n\n}());\n});\nvar smoothscroll_1 = smoothscroll.polyfill;\n\nvar _default$1 = /*#__PURE__*/function (_Core) {\n  _inherits(_default, _Core);\n\n  var _super = _createSuper(_default);\n\n  function _default() {\n    var _this;\n\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n    _classCallCheck(this, _default);\n\n    _this = _super.call(this, options);\n\n    if (_this.resetNativeScroll) {\n      if (history.scrollRestoration) {\n        history.scrollRestoration = 'manual';\n      }\n\n      window.scrollTo(0, 0);\n    }\n\n    window.addEventListener('scroll', _this.checkScroll, false);\n\n    if (window.smoothscrollPolyfill === undefined) {\n      window.smoothscrollPolyfill = smoothscroll;\n      window.smoothscrollPolyfill.polyfill();\n    }\n\n    return _this;\n  }\n\n  _createClass(_default, [{\n    key: \"init\",\n    value: function init() {\n      this.instance.scroll.y = window.pageYOffset;\n      this.addElements();\n      this.detectElements();\n\n      _get(_getPrototypeOf(_default.prototype), \"init\", this).call(this);\n    }\n  }, {\n    key: \"checkScroll\",\n    value: function checkScroll() {\n      var _this2 = this;\n\n      _get(_getPrototypeOf(_default.prototype), \"checkScroll\", this).call(this);\n\n      if (this.getDirection) {\n        this.addDirection();\n      }\n\n      if (this.getSpeed) {\n        this.addSpeed();\n        this.speedTs = Date.now();\n      }\n\n      this.instance.scroll.y = window.pageYOffset;\n\n      if (Object.entries(this.els).length) {\n        if (!this.hasScrollTicking) {\n          requestAnimationFrame(function () {\n            _this2.detectElements();\n          });\n          this.hasScrollTicking = true;\n        }\n      }\n    }\n  }, {\n    key: \"addDirection\",\n    value: function addDirection() {\n      if (window.pageYOffset > this.instance.scroll.y) {\n        if (this.instance.direction !== 'down') {\n          this.instance.direction = 'down';\n        }\n      } else if (window.pageYOffset < this.instance.scroll.y) {\n        if (this.instance.direction !== 'up') {\n          this.instance.direction = 'up';\n        }\n      }\n    }\n  }, {\n    key: \"addSpeed\",\n    value: function addSpeed() {\n      if (window.pageYOffset != this.instance.scroll.y) {\n        this.instance.speed = (window.pageYOffset - this.instance.scroll.y) / Math.max(1, Date.now() - this.speedTs);\n      } else {\n        this.instance.speed = 0;\n      }\n    }\n  }, {\n    key: \"resize\",\n    value: function resize() {\n      if (Object.entries(this.els).length) {\n        this.windowHeight = window.innerHeight;\n        this.updateElements();\n      }\n    }\n  }, {\n    key: \"addElements\",\n    value: function addElements() {\n      var _this3 = this;\n\n      this.els = {};\n      var els = this.el.querySelectorAll('[data-' + this.name + ']');\n      els.forEach(function (el, index) {\n        var BCR = el.getBoundingClientRect();\n        var cl = el.dataset[_this3.name + 'Class'] || _this3[\"class\"];\n        var id = typeof el.dataset[_this3.name + 'Id'] === 'string' ? el.dataset[_this3.name + 'Id'] : index;\n        var top;\n        var left;\n        var offset = typeof el.dataset[_this3.name + 'Offset'] === 'string' ? el.dataset[_this3.name + 'Offset'].split(',') : _this3.offset;\n        var repeat = el.dataset[_this3.name + 'Repeat'];\n        var call = el.dataset[_this3.name + 'Call'];\n        var target = el.dataset[_this3.name + 'Target'];\n        var targetEl;\n\n        if (target !== undefined) {\n          targetEl = document.querySelector(\"\".concat(target));\n        } else {\n          targetEl = el;\n        }\n\n        var targetElBCR = targetEl.getBoundingClientRect();\n        top = targetElBCR.top + _this3.instance.scroll.y;\n        left = targetElBCR.left + _this3.instance.scroll.x;\n        var bottom = top + targetEl.offsetHeight;\n        var right = left + targetEl.offsetWidth;\n\n        if (repeat == 'false') {\n          repeat = false;\n        } else if (repeat != undefined) {\n          repeat = true;\n        } else {\n          repeat = _this3.repeat;\n        }\n\n        var relativeOffset = _this3.getRelativeOffset(offset);\n\n        top = top + relativeOffset[0];\n        bottom = bottom - relativeOffset[1];\n        var mappedEl = {\n          el: el,\n          targetEl: targetEl,\n          id: id,\n          \"class\": cl,\n          top: top,\n          bottom: bottom,\n          left: left,\n          right: right,\n          offset: offset,\n          progress: 0,\n          repeat: repeat,\n          inView: false,\n          call: call\n        };\n        _this3.els[id] = mappedEl;\n\n        if (el.classList.contains(cl)) {\n          _this3.setInView(_this3.els[id], id);\n        }\n      });\n    }\n  }, {\n    key: \"updateElements\",\n    value: function updateElements() {\n      var _this4 = this;\n\n      Object.entries(this.els).forEach(function (_ref) {\n        var _ref2 = _slicedToArray(_ref, 2),\n            i = _ref2[0],\n            el = _ref2[1];\n\n        var top = el.targetEl.getBoundingClientRect().top + _this4.instance.scroll.y;\n\n        var bottom = top + el.targetEl.offsetHeight;\n\n        var relativeOffset = _this4.getRelativeOffset(el.offset);\n\n        _this4.els[i].top = top + relativeOffset[0];\n        _this4.els[i].bottom = bottom - relativeOffset[1];\n      });\n      this.hasScrollTicking = false;\n    }\n  }, {\n    key: \"getRelativeOffset\",\n    value: function getRelativeOffset(offset) {\n      var relativeOffset = [0, 0];\n\n      if (offset) {\n        for (var i = 0; i < offset.length; i++) {\n          if (typeof offset[i] == 'string') {\n            if (offset[i].includes('%')) {\n              relativeOffset[i] = parseInt(offset[i].replace('%', '') * this.windowHeight / 100);\n            } else {\n              relativeOffset[i] = parseInt(offset[i]);\n            }\n          } else {\n            relativeOffset[i] = offset[i];\n          }\n        }\n      }\n\n      return relativeOffset;\n    }\n    /**\n     * Scroll to a desired target.\n     *\n     * @param  Available options :\n     *          target {node, string, \"top\", \"bottom\", int} - The DOM element we want to scroll to\n     *          options {object} - Options object for additionnal settings.\n     * @return {void}\n     */\n\n  }, {\n    key: \"scrollTo\",\n    value: function scrollTo(target) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      // Parse options\n      var offset = parseInt(options.offset) || 0; // An offset to apply on top of given `target` or `sourceElem`'s target\n\n      var callback = options.callback ? options.callback : false; // function called when scrollTo completes (note that it won't wait for lerp to stabilize)\n\n      if (typeof target === 'string') {\n        // Selector or boundaries\n        if (target === 'top') {\n          target = this.html;\n        } else if (target === 'bottom') {\n          target = this.html.offsetHeight - window.innerHeight;\n        } else {\n          target = document.querySelector(target); // If the query fails, abort\n\n          if (!target) {\n            return;\n          }\n        }\n      } else if (typeof target === 'number') {\n        // Absolute coordinate\n        target = parseInt(target);\n      } else if (target && target.tagName) ; else {\n        console.warn('`target` parameter is not valid');\n        return;\n      } // We have a target that is not a coordinate yet, get it\n\n\n      if (typeof target !== 'number') {\n        offset = target.getBoundingClientRect().top + offset + this.instance.scroll.y;\n      } else {\n        offset = target + offset;\n      }\n\n      var isTargetReached = function isTargetReached() {\n        return parseInt(window.pageYOffset) === parseInt(offset);\n      };\n\n      if (callback) {\n        if (isTargetReached()) {\n          callback();\n          return;\n        } else {\n          var onScroll = function onScroll() {\n            if (isTargetReached()) {\n              window.removeEventListener('scroll', onScroll);\n              callback();\n            }\n          };\n\n          window.addEventListener('scroll', onScroll);\n        }\n      }\n\n      window.scrollTo({\n        top: offset,\n        behavior: options.duration === 0 ? 'auto' : 'smooth'\n      });\n    }\n  }, {\n    key: \"update\",\n    value: function update() {\n      this.addElements();\n      this.detectElements();\n    }\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      _get(_getPrototypeOf(_default.prototype), \"destroy\", this).call(this);\n\n      window.removeEventListener('scroll', this.checkScroll, false);\n    }\n  }]);\n\n  return _default;\n}(_default);\n\n/*\nobject-assign\n(c) Sindre Sorhus\n@license MIT\n*/\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nvar objectAssign = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n\nfunction E () {\n  // Keep this empty so it's easier to inherit from\n  // (via https://github.com/lipsmack from https://github.com/scottcorgan/tiny-emitter/issues/3)\n}\n\nE.prototype = {\n  on: function (name, callback, ctx) {\n    var e = this.e || (this.e = {});\n\n    (e[name] || (e[name] = [])).push({\n      fn: callback,\n      ctx: ctx\n    });\n\n    return this;\n  },\n\n  once: function (name, callback, ctx) {\n    var self = this;\n    function listener () {\n      self.off(name, listener);\n      callback.apply(ctx, arguments);\n    }\n    listener._ = callback;\n    return this.on(name, listener, ctx);\n  },\n\n  emit: function (name) {\n    var data = [].slice.call(arguments, 1);\n    var evtArr = ((this.e || (this.e = {}))[name] || []).slice();\n    var i = 0;\n    var len = evtArr.length;\n\n    for (i; i < len; i++) {\n      evtArr[i].fn.apply(evtArr[i].ctx, data);\n    }\n\n    return this;\n  },\n\n  off: function (name, callback) {\n    var e = this.e || (this.e = {});\n    var evts = e[name];\n    var liveEvents = [];\n\n    if (evts && callback) {\n      for (var i = 0, len = evts.length; i < len; i++) {\n        if (evts[i].fn !== callback && evts[i].fn._ !== callback)\n          liveEvents.push(evts[i]);\n      }\n    }\n\n    // Remove event from queue to prevent memory leak\n    // Suggested by https://github.com/lazd\n    // Ref: https://github.com/scottcorgan/tiny-emitter/commit/c6ebfaa9bc973b33d110a84a307742b7cf94c953#commitcomment-5024910\n\n    (liveEvents.length)\n      ? e[name] = liveEvents\n      : delete e[name];\n\n    return this;\n  }\n};\n\nvar tinyEmitter = E;\n\nvar lethargy = createCommonjsModule(function (module, exports) {\n// Generated by CoffeeScript 1.9.2\n(function() {\n  var root;\n\n  root =  exports !== null ? exports : this;\n\n  root.Lethargy = (function() {\n    function Lethargy(stability, sensitivity, tolerance, delay) {\n      this.stability = stability != null ? Math.abs(stability) : 8;\n      this.sensitivity = sensitivity != null ? 1 + Math.abs(sensitivity) : 100;\n      this.tolerance = tolerance != null ? 1 + Math.abs(tolerance) : 1.1;\n      this.delay = delay != null ? delay : 150;\n      this.lastUpDeltas = (function() {\n        var i, ref, results;\n        results = [];\n        for (i = 1, ref = this.stability * 2; 1 <= ref ? i <= ref : i >= ref; 1 <= ref ? i++ : i--) {\n          results.push(null);\n        }\n        return results;\n      }).call(this);\n      this.lastDownDeltas = (function() {\n        var i, ref, results;\n        results = [];\n        for (i = 1, ref = this.stability * 2; 1 <= ref ? i <= ref : i >= ref; 1 <= ref ? i++ : i--) {\n          results.push(null);\n        }\n        return results;\n      }).call(this);\n      this.deltasTimestamp = (function() {\n        var i, ref, results;\n        results = [];\n        for (i = 1, ref = this.stability * 2; 1 <= ref ? i <= ref : i >= ref; 1 <= ref ? i++ : i--) {\n          results.push(null);\n        }\n        return results;\n      }).call(this);\n    }\n\n    Lethargy.prototype.check = function(e) {\n      var lastDelta;\n      e = e.originalEvent || e;\n      if (e.wheelDelta != null) {\n        lastDelta = e.wheelDelta;\n      } else if (e.deltaY != null) {\n        lastDelta = e.deltaY * -40;\n      } else if ((e.detail != null) || e.detail === 0) {\n        lastDelta = e.detail * -40;\n      }\n      this.deltasTimestamp.push(Date.now());\n      this.deltasTimestamp.shift();\n      if (lastDelta > 0) {\n        this.lastUpDeltas.push(lastDelta);\n        this.lastUpDeltas.shift();\n        return this.isInertia(1);\n      } else {\n        this.lastDownDeltas.push(lastDelta);\n        this.lastDownDeltas.shift();\n        return this.isInertia(-1);\n      }\n    };\n\n    Lethargy.prototype.isInertia = function(direction) {\n      var lastDeltas, lastDeltasNew, lastDeltasOld, newAverage, newSum, oldAverage, oldSum;\n      lastDeltas = direction === -1 ? this.lastDownDeltas : this.lastUpDeltas;\n      if (lastDeltas[0] === null) {\n        return direction;\n      }\n      if (this.deltasTimestamp[(this.stability * 2) - 2] + this.delay > Date.now() && lastDeltas[0] === lastDeltas[(this.stability * 2) - 1]) {\n        return false;\n      }\n      lastDeltasOld = lastDeltas.slice(0, this.stability);\n      lastDeltasNew = lastDeltas.slice(this.stability, this.stability * 2);\n      oldSum = lastDeltasOld.reduce(function(t, s) {\n        return t + s;\n      });\n      newSum = lastDeltasNew.reduce(function(t, s) {\n        return t + s;\n      });\n      oldAverage = oldSum / lastDeltasOld.length;\n      newAverage = newSum / lastDeltasNew.length;\n      if (Math.abs(oldAverage) < Math.abs(newAverage * this.tolerance) && (this.sensitivity < Math.abs(newAverage))) {\n        return direction;\n      } else {\n        return false;\n      }\n    };\n\n    Lethargy.prototype.showLastUpDeltas = function() {\n      return this.lastUpDeltas;\n    };\n\n    Lethargy.prototype.showLastDownDeltas = function() {\n      return this.lastDownDeltas;\n    };\n\n    return Lethargy;\n\n  })();\n\n}).call(commonjsGlobal);\n});\n\nvar support = (function getSupport() {\n    return {\n        hasWheelEvent: 'onwheel' in document,\n        hasMouseWheelEvent: 'onmousewheel' in document,\n        hasTouch: ('ontouchstart' in window) || window.TouchEvent || window.DocumentTouch && document instanceof DocumentTouch,\n        hasTouchWin: navigator.msMaxTouchPoints && navigator.msMaxTouchPoints > 1,\n        hasPointer: !!window.navigator.msPointerEnabled,\n        hasKeyDown: 'onkeydown' in document,\n        isFirefox: navigator.userAgent.indexOf('Firefox') > -1\n    };\n})();\n\nvar toString = Object.prototype.toString,\n    hasOwnProperty$1 = Object.prototype.hasOwnProperty;\n\nvar bindallStandalone = function(object) {\n    if(!object) return console.warn('bindAll requires at least one argument.');\n\n    var functions = Array.prototype.slice.call(arguments, 1);\n\n    if (functions.length === 0) {\n\n        for (var method in object) {\n            if(hasOwnProperty$1.call(object, method)) {\n                if(typeof object[method] == 'function' && toString.call(object[method]) == \"[object Function]\") {\n                    functions.push(method);\n                }\n            }\n        }\n    }\n\n    for(var i = 0; i < functions.length; i++) {\n        var f = functions[i];\n        object[f] = bind(object[f], object);\n    }\n};\n\n/*\n    Faster bind without specific-case checking. (see https://coderwall.com/p/oi3j3w).\n    bindAll is only needed for events binding so no need to make slow fixes for constructor\n    or partial application.\n*/\nfunction bind(func, context) {\n  return function() {\n    return func.apply(context, arguments);\n  };\n}\n\nvar Lethargy = lethargy.Lethargy;\n\n\n\nvar EVT_ID = 'virtualscroll';\n\nvar src = VirtualScroll;\n\nvar keyCodes = {\n    LEFT: 37,\n    UP: 38,\n    RIGHT: 39,\n    DOWN: 40,\n    SPACE: 32\n};\n\nfunction VirtualScroll(options) {\n    bindallStandalone(this, '_onWheel', '_onMouseWheel', '_onTouchStart', '_onTouchMove', '_onKeyDown');\n\n    this.el = window;\n    if (options && options.el) {\n        this.el = options.el;\n        delete options.el;\n    }\n    this.options = objectAssign({\n        mouseMultiplier: 1,\n        touchMultiplier: 2,\n        firefoxMultiplier: 15,\n        keyStep: 120,\n        preventTouch: false,\n        unpreventTouchClass: 'vs-touchmove-allowed',\n        limitInertia: false,\n        useKeyboard: true,\n        useTouch: true\n    }, options);\n\n    if (this.options.limitInertia) this._lethargy = new Lethargy();\n\n    this._emitter = new tinyEmitter();\n    this._event = {\n        y: 0,\n        x: 0,\n        deltaX: 0,\n        deltaY: 0\n    };\n    this.touchStartX = null;\n    this.touchStartY = null;\n    this.bodyTouchAction = null;\n\n    if (this.options.passive !== undefined) {\n        this.listenerOptions = {passive: this.options.passive};\n    }\n}\n\nVirtualScroll.prototype._notify = function(e) {\n    var evt = this._event;\n    evt.x += evt.deltaX;\n    evt.y += evt.deltaY;\n\n   this._emitter.emit(EVT_ID, {\n        x: evt.x,\n        y: evt.y,\n        deltaX: evt.deltaX,\n        deltaY: evt.deltaY,\n        originalEvent: e\n   });\n};\n\nVirtualScroll.prototype._onWheel = function(e) {\n    var options = this.options;\n    if (this._lethargy && this._lethargy.check(e) === false) return;\n    var evt = this._event;\n\n    // In Chrome and in Firefox (at least the new one)\n    evt.deltaX = e.wheelDeltaX || e.deltaX * -1;\n    evt.deltaY = e.wheelDeltaY || e.deltaY * -1;\n\n    // for our purpose deltamode = 1 means user is on a wheel mouse, not touch pad\n    // real meaning: https://developer.mozilla.org/en-US/docs/Web/API/WheelEvent#Delta_modes\n    if(support.isFirefox && e.deltaMode == 1) {\n        evt.deltaX *= options.firefoxMultiplier;\n        evt.deltaY *= options.firefoxMultiplier;\n    }\n\n    evt.deltaX *= options.mouseMultiplier;\n    evt.deltaY *= options.mouseMultiplier;\n\n    this._notify(e);\n};\n\nVirtualScroll.prototype._onMouseWheel = function(e) {\n    if (this.options.limitInertia && this._lethargy.check(e) === false) return;\n\n    var evt = this._event;\n\n    // In Safari, IE and in Chrome if 'wheel' isn't defined\n    evt.deltaX = (e.wheelDeltaX) ? e.wheelDeltaX : 0;\n    evt.deltaY = (e.wheelDeltaY) ? e.wheelDeltaY : e.wheelDelta;\n\n    this._notify(e);\n};\n\nVirtualScroll.prototype._onTouchStart = function(e) {\n    var t = (e.targetTouches) ? e.targetTouches[0] : e;\n    this.touchStartX = t.pageX;\n    this.touchStartY = t.pageY;\n};\n\nVirtualScroll.prototype._onTouchMove = function(e) {\n    var options = this.options;\n    if(options.preventTouch\n        && !e.target.classList.contains(options.unpreventTouchClass)) {\n        e.preventDefault();\n    }\n\n    var evt = this._event;\n\n    var t = (e.targetTouches) ? e.targetTouches[0] : e;\n\n    evt.deltaX = (t.pageX - this.touchStartX) * options.touchMultiplier;\n    evt.deltaY = (t.pageY - this.touchStartY) * options.touchMultiplier;\n\n    this.touchStartX = t.pageX;\n    this.touchStartY = t.pageY;\n\n    this._notify(e);\n};\n\nVirtualScroll.prototype._onKeyDown = function(e) {\n    var evt = this._event;\n    evt.deltaX = evt.deltaY = 0;\n    var windowHeight = window.innerHeight - 40;\n\n    switch(e.keyCode) {\n        case keyCodes.LEFT:\n        case keyCodes.UP:\n            evt.deltaY = this.options.keyStep;\n            break;\n\n        case keyCodes.RIGHT:\n        case keyCodes.DOWN:\n            evt.deltaY = - this.options.keyStep;\n            break;\n        case  e.shiftKey:\n            evt.deltaY = windowHeight;\n            break;\n        case keyCodes.SPACE:\n            evt.deltaY = - windowHeight;\n            break;\n        default:\n            return;\n    }\n\n    this._notify(e);\n};\n\nVirtualScroll.prototype._bind = function() {\n    if(support.hasWheelEvent) this.el.addEventListener('wheel', this._onWheel, this.listenerOptions);\n    if(support.hasMouseWheelEvent) this.el.addEventListener('mousewheel', this._onMouseWheel, this.listenerOptions);\n\n    if(support.hasTouch && this.options.useTouch) {\n        this.el.addEventListener('touchstart', this._onTouchStart, this.listenerOptions);\n        this.el.addEventListener('touchmove', this._onTouchMove, this.listenerOptions);\n    }\n\n    if(support.hasPointer && support.hasTouchWin) {\n        this.bodyTouchAction = document.body.style.msTouchAction;\n        document.body.style.msTouchAction = 'none';\n        this.el.addEventListener('MSPointerDown', this._onTouchStart, true);\n        this.el.addEventListener('MSPointerMove', this._onTouchMove, true);\n    }\n\n    if(support.hasKeyDown && this.options.useKeyboard) document.addEventListener('keydown', this._onKeyDown);\n};\n\nVirtualScroll.prototype._unbind = function() {\n    if(support.hasWheelEvent) this.el.removeEventListener('wheel', this._onWheel);\n    if(support.hasMouseWheelEvent) this.el.removeEventListener('mousewheel', this._onMouseWheel);\n\n    if(support.hasTouch) {\n        this.el.removeEventListener('touchstart', this._onTouchStart);\n        this.el.removeEventListener('touchmove', this._onTouchMove);\n    }\n\n    if(support.hasPointer && support.hasTouchWin) {\n        document.body.style.msTouchAction = this.bodyTouchAction;\n        this.el.removeEventListener('MSPointerDown', this._onTouchStart, true);\n        this.el.removeEventListener('MSPointerMove', this._onTouchMove, true);\n    }\n\n    if(support.hasKeyDown && this.options.useKeyboard) document.removeEventListener('keydown', this._onKeyDown);\n};\n\nVirtualScroll.prototype.on = function(cb, ctx) {\n  this._emitter.on(EVT_ID, cb, ctx);\n\n  var events = this._emitter.e;\n  if (events && events[EVT_ID] && events[EVT_ID].length === 1) this._bind();\n};\n\nVirtualScroll.prototype.off = function(cb, ctx) {\n  this._emitter.off(EVT_ID, cb, ctx);\n\n  var events = this._emitter.e;\n  if (!events[EVT_ID] || events[EVT_ID].length <= 0) this._unbind();\n};\n\nVirtualScroll.prototype.reset = function() {\n    var evt = this._event;\n    evt.x = 0;\n    evt.y = 0;\n};\n\nVirtualScroll.prototype.destroy = function() {\n    this._emitter.off();\n    this._unbind();\n};\n\nfunction lerp(start, end, amt) {\n  return (1 - amt) * start + amt * end;\n}\n\nfunction getTranslate(el) {\n  var translate = {};\n  if (!window.getComputedStyle) return;\n  var style = getComputedStyle(el);\n  var transform = style.transform || style.webkitTransform || style.mozTransform;\n  var mat = transform.match(/^matrix3d\\((.+)\\)$/);\n\n  if (mat) {\n    translate.x = mat ? parseFloat(mat[1].split(', ')[12]) : 0;\n    translate.y = mat ? parseFloat(mat[1].split(', ')[13]) : 0;\n  } else {\n    mat = transform.match(/^matrix\\((.+)\\)$/);\n    translate.x = mat ? parseFloat(mat[1].split(', ')[4]) : 0;\n    translate.y = mat ? parseFloat(mat[1].split(', ')[5]) : 0;\n  }\n\n  return translate;\n}\n\n/**\n * Returns an array containing all the parent nodes of the given node\n * @param  {object} node\n * @return {array} parent nodes\n */\nfunction getParents(elem) {\n  // Set up a parent array\n  var parents = []; // Push each parent element to the array\n\n  for (; elem && elem !== document; elem = elem.parentNode) {\n    parents.push(elem);\n  } // Return our parent array\n\n\n  return parents;\n} // https://gomakethings.com/how-to-get-the-closest-parent-element-with-a-matching-selector-using-vanilla-javascript/\n\n/**\n * https://github.com/gre/bezier-easing\n * BezierEasing - use bezier curve for transition easing function\n * by Gaëtan Renaudeau 2014 - 2015 – MIT License\n */\n\n// These values are established by empiricism with tests (tradeoff: performance VS precision)\nvar NEWTON_ITERATIONS = 4;\nvar NEWTON_MIN_SLOPE = 0.001;\nvar SUBDIVISION_PRECISION = 0.0000001;\nvar SUBDIVISION_MAX_ITERATIONS = 10;\n\nvar kSplineTableSize = 11;\nvar kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);\n\nvar float32ArraySupported = typeof Float32Array === 'function';\n\nfunction A (aA1, aA2) { return 1.0 - 3.0 * aA2 + 3.0 * aA1; }\nfunction B (aA1, aA2) { return 3.0 * aA2 - 6.0 * aA1; }\nfunction C (aA1)      { return 3.0 * aA1; }\n\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nfunction calcBezier (aT, aA1, aA2) { return ((A(aA1, aA2) * aT + B(aA1, aA2)) * aT + C(aA1)) * aT; }\n\n// Returns dx/dt given t, x1, and x2, or dy/dt given t, y1, and y2.\nfunction getSlope (aT, aA1, aA2) { return 3.0 * A(aA1, aA2) * aT * aT + 2.0 * B(aA1, aA2) * aT + C(aA1); }\n\nfunction binarySubdivide (aX, aA, aB, mX1, mX2) {\n  var currentX, currentT, i = 0;\n  do {\n    currentT = aA + (aB - aA) / 2.0;\n    currentX = calcBezier(currentT, mX1, mX2) - aX;\n    if (currentX > 0.0) {\n      aB = currentT;\n    } else {\n      aA = currentT;\n    }\n  } while (Math.abs(currentX) > SUBDIVISION_PRECISION && ++i < SUBDIVISION_MAX_ITERATIONS);\n  return currentT;\n}\n\nfunction newtonRaphsonIterate (aX, aGuessT, mX1, mX2) {\n for (var i = 0; i < NEWTON_ITERATIONS; ++i) {\n   var currentSlope = getSlope(aGuessT, mX1, mX2);\n   if (currentSlope === 0.0) {\n     return aGuessT;\n   }\n   var currentX = calcBezier(aGuessT, mX1, mX2) - aX;\n   aGuessT -= currentX / currentSlope;\n }\n return aGuessT;\n}\n\nfunction LinearEasing (x) {\n  return x;\n}\n\nvar src$1 = function bezier (mX1, mY1, mX2, mY2) {\n  if (!(0 <= mX1 && mX1 <= 1 && 0 <= mX2 && mX2 <= 1)) {\n    throw new Error('bezier x values must be in [0, 1] range');\n  }\n\n  if (mX1 === mY1 && mX2 === mY2) {\n    return LinearEasing;\n  }\n\n  // Precompute samples table\n  var sampleValues = float32ArraySupported ? new Float32Array(kSplineTableSize) : new Array(kSplineTableSize);\n  for (var i = 0; i < kSplineTableSize; ++i) {\n    sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);\n  }\n\n  function getTForX (aX) {\n    var intervalStart = 0.0;\n    var currentSample = 1;\n    var lastSample = kSplineTableSize - 1;\n\n    for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {\n      intervalStart += kSampleStepSize;\n    }\n    --currentSample;\n\n    // Interpolate to provide an initial guess for t\n    var dist = (aX - sampleValues[currentSample]) / (sampleValues[currentSample + 1] - sampleValues[currentSample]);\n    var guessForT = intervalStart + dist * kSampleStepSize;\n\n    var initialSlope = getSlope(guessForT, mX1, mX2);\n    if (initialSlope >= NEWTON_MIN_SLOPE) {\n      return newtonRaphsonIterate(aX, guessForT, mX1, mX2);\n    } else if (initialSlope === 0.0) {\n      return guessForT;\n    } else {\n      return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);\n    }\n  }\n\n  return function BezierEasing (x) {\n    // Because JavaScript number are imprecise, we should guarantee the extremes are right.\n    if (x === 0) {\n      return 0;\n    }\n    if (x === 1) {\n      return 1;\n    }\n    return calcBezier(getTForX(x), mY1, mY2);\n  };\n};\n\nvar keyCodes$1 = {\n  LEFT: 37,\n  UP: 38,\n  RIGHT: 39,\n  DOWN: 40,\n  SPACE: 32,\n  TAB: 9,\n  PAGEUP: 33,\n  PAGEDOWN: 34,\n  HOME: 36,\n  END: 35\n};\n\nvar _default$2 = /*#__PURE__*/function (_Core) {\n  _inherits(_default, _Core);\n\n  var _super = _createSuper(_default);\n\n  function _default() {\n    var _this;\n\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n    _classCallCheck(this, _default);\n\n    if (history.scrollRestoration) {\n      history.scrollRestoration = 'manual';\n    }\n\n    window.scrollTo(0, 0);\n    _this = _super.call(this, options);\n    if (_this.inertia) _this.lerp = _this.inertia * 0.1;\n    _this.isScrolling = false;\n    _this.isDraggingScrollbar = false;\n    _this.isTicking = false;\n    _this.hasScrollTicking = false;\n    _this.parallaxElements = {};\n    _this.stop = false;\n    _this.scrollbarContainer = options.scrollbarContainer;\n    _this.checkKey = _this.checkKey.bind(_assertThisInitialized(_this));\n    window.addEventListener('keydown', _this.checkKey, false);\n    return _this;\n  }\n\n  _createClass(_default, [{\n    key: \"init\",\n    value: function init() {\n      var _this2 = this;\n\n      this.html.classList.add(this.smoothClass);\n      this.html.setAttribute(\"data-\".concat(this.name, \"-direction\"), this.direction);\n      this.instance = _objectSpread2({\n        delta: {\n          x: this.initPosition.x,\n          y: this.initPosition.y\n        },\n        scroll: {\n          x: this.initPosition.x,\n          y: this.initPosition.y\n        }\n      }, this.instance);\n      this.vs = new src({\n        el: this.scrollFromAnywhere ? document : this.el,\n        mouseMultiplier: navigator.platform.indexOf('Win') > -1 ? 1 : 0.4,\n        firefoxMultiplier: this.firefoxMultiplier,\n        touchMultiplier: this.touchMultiplier,\n        useKeyboard: false,\n        passive: true\n      });\n      this.vs.on(function (e) {\n        if (_this2.stop) {\n          return;\n        }\n\n        if (!_this2.isDraggingScrollbar) {\n          requestAnimationFrame(function () {\n            _this2.updateDelta(e);\n\n            if (!_this2.isScrolling) _this2.startScrolling();\n          });\n        }\n      });\n      this.setScrollLimit();\n      this.initScrollBar();\n      this.addSections();\n      this.addElements();\n      this.checkScroll(true);\n      this.transformElements(true, true);\n\n      _get(_getPrototypeOf(_default.prototype), \"init\", this).call(this);\n    }\n  }, {\n    key: \"setScrollLimit\",\n    value: function setScrollLimit() {\n      this.instance.limit.y = this.el.offsetHeight - this.windowHeight;\n\n      if (this.direction === 'horizontal') {\n        var totalWidth = 0;\n        var nodes = this.el.children;\n\n        for (var i = 0; i < nodes.length; i++) {\n          totalWidth += nodes[i].offsetWidth;\n        }\n\n        this.instance.limit.x = totalWidth - this.windowWidth;\n      }\n    }\n  }, {\n    key: \"startScrolling\",\n    value: function startScrolling() {\n      this.startScrollTs = Date.now(); // Record timestamp\n\n      this.isScrolling = true;\n      this.checkScroll();\n      this.html.classList.add(this.scrollingClass);\n    }\n  }, {\n    key: \"stopScrolling\",\n    value: function stopScrolling() {\n      cancelAnimationFrame(this.checkScrollRaf); // Prevent checkScroll to continue looping\n      //Pevent scrollbar glitch/locking\n\n      this.startScrollTs = undefined;\n\n      if (this.scrollToRaf) {\n        cancelAnimationFrame(this.scrollToRaf);\n        this.scrollToRaf = null;\n      }\n\n      this.isScrolling = false;\n      this.instance.scroll.y = Math.round(this.instance.scroll.y);\n      this.html.classList.remove(this.scrollingClass);\n    }\n  }, {\n    key: \"checkKey\",\n    value: function checkKey(e) {\n      var _this3 = this;\n\n      if (this.stop) {\n        // If we are stopped, we don't want any scroll to occur because of a keypress\n        // Prevent tab to scroll to activeElement\n        if (e.keyCode == keyCodes$1.TAB) {\n          requestAnimationFrame(function () {\n            // Make sure native scroll is always at top of page\n            _this3.html.scrollTop = 0;\n            document.body.scrollTop = 0;\n            _this3.html.scrollLeft = 0;\n            document.body.scrollLeft = 0;\n          });\n        }\n\n        return;\n      }\n\n      switch (e.keyCode) {\n        case keyCodes$1.TAB:\n          // Do not remove the RAF\n          // It allows to override the browser's native scrollTo, which is essential\n          requestAnimationFrame(function () {\n            // Make sure native scroll is always at top of page\n            _this3.html.scrollTop = 0;\n            document.body.scrollTop = 0;\n            _this3.html.scrollLeft = 0;\n            document.body.scrollLeft = 0; // Request scrollTo on the focusedElement, putting it at the center of the screen\n\n            _this3.scrollTo(document.activeElement, {\n              offset: -window.innerHeight / 2\n            });\n          });\n          break;\n\n        case keyCodes$1.UP:\n          if (this.isActiveElementScrollSensitive()) {\n            this.instance.delta[this.directionAxis] -= 240;\n          }\n\n          break;\n\n        case keyCodes$1.DOWN:\n          if (this.isActiveElementScrollSensitive()) {\n            this.instance.delta[this.directionAxis] += 240;\n          }\n\n          break;\n\n        case keyCodes$1.PAGEUP:\n          this.instance.delta[this.directionAxis] -= window.innerHeight;\n          break;\n\n        case keyCodes$1.PAGEDOWN:\n          this.instance.delta[this.directionAxis] += window.innerHeight;\n          break;\n\n        case keyCodes$1.HOME:\n          this.instance.delta[this.directionAxis] -= this.instance.limit[this.directionAxis];\n          break;\n\n        case keyCodes$1.END:\n          this.instance.delta[this.directionAxis] += this.instance.limit[this.directionAxis];\n          break;\n\n        case keyCodes$1.SPACE:\n          if (this.isActiveElementScrollSensitive()) {\n            if (e.shiftKey) {\n              this.instance.delta[this.directionAxis] -= window.innerHeight;\n            } else {\n              this.instance.delta[this.directionAxis] += window.innerHeight;\n            }\n          }\n\n          break;\n\n        default:\n          return;\n      }\n\n      if (this.instance.delta[this.directionAxis] < 0) this.instance.delta[this.directionAxis] = 0;\n      if (this.instance.delta[this.directionAxis] > this.instance.limit[this.directionAxis]) this.instance.delta[this.directionAxis] = this.instance.limit[this.directionAxis];\n      this.stopScrolling(); // Stop any movement, allows to kill any other `scrollTo` still happening\n\n      this.isScrolling = true;\n      this.checkScroll();\n      this.html.classList.add(this.scrollingClass);\n    }\n  }, {\n    key: \"isActiveElementScrollSensitive\",\n    value: function isActiveElementScrollSensitive() {\n      return !(document.activeElement instanceof HTMLInputElement) && !(document.activeElement instanceof HTMLTextAreaElement) && !(document.activeElement instanceof HTMLButtonElement) && !(document.activeElement instanceof HTMLSelectElement);\n    }\n  }, {\n    key: \"checkScroll\",\n    value: function checkScroll() {\n      var _this4 = this;\n\n      var forced = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n\n      if (forced || this.isScrolling || this.isDraggingScrollbar) {\n        if (!this.hasScrollTicking) {\n          this.checkScrollRaf = requestAnimationFrame(function () {\n            return _this4.checkScroll();\n          });\n          this.hasScrollTicking = true;\n        }\n\n        this.updateScroll();\n        var distance = Math.abs(this.instance.delta[this.directionAxis] - this.instance.scroll[this.directionAxis]);\n        var timeSinceStart = Date.now() - this.startScrollTs; // Get the time since the scroll was started: the scroll can be stopped again only past 100ms\n\n        if (!this.animatingScroll && timeSinceStart > 100 && (distance < 0.5 && this.instance.delta[this.directionAxis] != 0 || distance < 0.5 && this.instance.delta[this.directionAxis] == 0)) {\n          this.stopScrolling();\n        }\n\n        Object.entries(this.sections).forEach(function (_ref) {\n          var _ref2 = _slicedToArray(_ref, 2),\n              i = _ref2[0],\n              section = _ref2[1];\n\n          if (section.persistent || _this4.instance.scroll[_this4.directionAxis] > section.offset[_this4.directionAxis] && _this4.instance.scroll[_this4.directionAxis] < section.limit[_this4.directionAxis]) {\n            if (_this4.direction === 'horizontal') {\n              _this4.transform(section.el, -_this4.instance.scroll[_this4.directionAxis], 0);\n            } else {\n              _this4.transform(section.el, 0, -_this4.instance.scroll[_this4.directionAxis]);\n            }\n\n            if (!section.inView) {\n              section.inView = true;\n              section.el.style.opacity = 1;\n              section.el.style.pointerEvents = 'all';\n              section.el.setAttribute(\"data-\".concat(_this4.name, \"-section-inview\"), '');\n            }\n          } else {\n            if (section.inView || forced) {\n              section.inView = false;\n              section.el.style.opacity = 0;\n              section.el.style.pointerEvents = 'none';\n              section.el.removeAttribute(\"data-\".concat(_this4.name, \"-section-inview\"));\n            }\n\n            _this4.transform(section.el, 0, 0);\n          }\n        });\n\n        if (this.getDirection) {\n          this.addDirection();\n        }\n\n        if (this.getSpeed) {\n          this.addSpeed();\n          this.speedTs = Date.now();\n        }\n\n        this.detectElements();\n        this.transformElements();\n\n        if (this.hasScrollbar) {\n          var scrollBarTranslation = this.instance.scroll[this.directionAxis] / this.instance.limit[this.directionAxis] * this.scrollBarLimit[this.directionAxis];\n\n          if (this.direction === 'horizontal') {\n            this.transform(this.scrollbarThumb, scrollBarTranslation, 0);\n          } else {\n            this.transform(this.scrollbarThumb, 0, scrollBarTranslation);\n          }\n        }\n\n        _get(_getPrototypeOf(_default.prototype), \"checkScroll\", this).call(this);\n\n        this.hasScrollTicking = false;\n      }\n    }\n  }, {\n    key: \"resize\",\n    value: function resize() {\n      this.windowHeight = window.innerHeight;\n      this.windowWidth = window.innerWidth;\n      this.checkContext();\n      this.windowMiddle = {\n        x: this.windowWidth / 2,\n        y: this.windowHeight / 2\n      };\n      this.update();\n    }\n  }, {\n    key: \"updateDelta\",\n    value: function updateDelta(e) {\n      var delta;\n      var gestureDirection = this[this.context] && this[this.context].gestureDirection ? this[this.context].gestureDirection : this.gestureDirection;\n\n      if (gestureDirection === 'both') {\n        delta = e.deltaX + e.deltaY;\n      } else if (gestureDirection === 'vertical') {\n        delta = e.deltaY;\n      } else if (gestureDirection === 'horizontal') {\n        delta = e.deltaX;\n      } else {\n        delta = e.deltaY;\n      }\n\n      this.instance.delta[this.directionAxis] -= delta * this.multiplier;\n      if (this.instance.delta[this.directionAxis] < 0) this.instance.delta[this.directionAxis] = 0;\n      if (this.instance.delta[this.directionAxis] > this.instance.limit[this.directionAxis]) this.instance.delta[this.directionAxis] = this.instance.limit[this.directionAxis];\n    }\n  }, {\n    key: \"updateScroll\",\n    value: function updateScroll(e) {\n      if (this.isScrolling || this.isDraggingScrollbar) {\n        this.instance.scroll[this.directionAxis] = lerp(this.instance.scroll[this.directionAxis], this.instance.delta[this.directionAxis], this.lerp);\n      } else {\n        if (this.instance.scroll[this.directionAxis] > this.instance.limit[this.directionAxis]) {\n          this.setScroll(this.instance.scroll[this.directionAxis], this.instance.limit[this.directionAxis]);\n        } else if (this.instance.scroll.y < 0) {\n          this.setScroll(this.instance.scroll[this.directionAxis], 0);\n        } else {\n          this.setScroll(this.instance.scroll[this.directionAxis], this.instance.delta[this.directionAxis]);\n        }\n      }\n    }\n  }, {\n    key: \"addDirection\",\n    value: function addDirection() {\n      if (this.instance.delta.y > this.instance.scroll.y) {\n        if (this.instance.direction !== 'down') {\n          this.instance.direction = 'down';\n        }\n      } else if (this.instance.delta.y < this.instance.scroll.y) {\n        if (this.instance.direction !== 'up') {\n          this.instance.direction = 'up';\n        }\n      }\n\n      if (this.instance.delta.x > this.instance.scroll.x) {\n        if (this.instance.direction !== 'right') {\n          this.instance.direction = 'right';\n        }\n      } else if (this.instance.delta.x < this.instance.scroll.x) {\n        if (this.instance.direction !== 'left') {\n          this.instance.direction = 'left';\n        }\n      }\n    }\n  }, {\n    key: \"addSpeed\",\n    value: function addSpeed() {\n      if (this.instance.delta[this.directionAxis] != this.instance.scroll[this.directionAxis]) {\n        this.instance.speed = (this.instance.delta[this.directionAxis] - this.instance.scroll[this.directionAxis]) / Math.max(1, Date.now() - this.speedTs);\n      } else {\n        this.instance.speed = 0;\n      }\n    }\n  }, {\n    key: \"initScrollBar\",\n    value: function initScrollBar() {\n      this.scrollbar = document.createElement('span');\n      this.scrollbarThumb = document.createElement('span');\n      this.scrollbar.classList.add(\"\".concat(this.scrollbarClass));\n      this.scrollbarThumb.classList.add(\"\".concat(this.scrollbarClass, \"_thumb\"));\n      this.scrollbar.append(this.scrollbarThumb);\n\n      if (this.scrollbarContainer) {\n        this.scrollbarContainer.append(this.scrollbar);\n      } else {\n        document.body.append(this.scrollbar);\n      } // Scrollbar Events\n\n\n      this.getScrollBar = this.getScrollBar.bind(this);\n      this.releaseScrollBar = this.releaseScrollBar.bind(this);\n      this.moveScrollBar = this.moveScrollBar.bind(this);\n      this.scrollbarThumb.addEventListener('mousedown', this.getScrollBar);\n      window.addEventListener('mouseup', this.releaseScrollBar);\n      window.addEventListener('mousemove', this.moveScrollBar); // Set scrollbar values\n\n      this.hasScrollbar = false;\n\n      if (this.direction == 'horizontal') {\n        if (this.instance.limit.x + this.windowWidth <= this.windowWidth) {\n          return;\n        }\n      } else {\n        if (this.instance.limit.y + this.windowHeight <= this.windowHeight) {\n          return;\n        }\n      }\n\n      this.hasScrollbar = true;\n      this.scrollbarBCR = this.scrollbar.getBoundingClientRect();\n      this.scrollbarHeight = this.scrollbarBCR.height;\n      this.scrollbarWidth = this.scrollbarBCR.width;\n\n      if (this.direction === 'horizontal') {\n        this.scrollbarThumb.style.width = \"\".concat(this.scrollbarWidth * this.scrollbarWidth / (this.instance.limit.x + this.scrollbarWidth), \"px\");\n      } else {\n        this.scrollbarThumb.style.height = \"\".concat(this.scrollbarHeight * this.scrollbarHeight / (this.instance.limit.y + this.scrollbarHeight), \"px\");\n      }\n\n      this.scrollbarThumbBCR = this.scrollbarThumb.getBoundingClientRect();\n      this.scrollBarLimit = {\n        x: this.scrollbarWidth - this.scrollbarThumbBCR.width,\n        y: this.scrollbarHeight - this.scrollbarThumbBCR.height\n      };\n    }\n  }, {\n    key: \"reinitScrollBar\",\n    value: function reinitScrollBar() {\n      this.hasScrollbar = false;\n\n      if (this.direction == 'horizontal') {\n        if (this.instance.limit.x + this.windowWidth <= this.windowWidth) {\n          return;\n        }\n      } else {\n        if (this.instance.limit.y + this.windowHeight <= this.windowHeight) {\n          return;\n        }\n      }\n\n      this.hasScrollbar = true;\n      this.scrollbarBCR = this.scrollbar.getBoundingClientRect();\n      this.scrollbarHeight = this.scrollbarBCR.height;\n      this.scrollbarWidth = this.scrollbarBCR.width;\n\n      if (this.direction === 'horizontal') {\n        this.scrollbarThumb.style.width = \"\".concat(this.scrollbarWidth * this.scrollbarWidth / (this.instance.limit.x + this.scrollbarWidth), \"px\");\n      } else {\n        this.scrollbarThumb.style.height = \"\".concat(this.scrollbarHeight * this.scrollbarHeight / (this.instance.limit.y + this.scrollbarHeight), \"px\");\n      }\n\n      this.scrollbarThumbBCR = this.scrollbarThumb.getBoundingClientRect();\n      this.scrollBarLimit = {\n        x: this.scrollbarWidth - this.scrollbarThumbBCR.width,\n        y: this.scrollbarHeight - this.scrollbarThumbBCR.height\n      };\n    }\n  }, {\n    key: \"destroyScrollBar\",\n    value: function destroyScrollBar() {\n      this.scrollbarThumb.removeEventListener('mousedown', this.getScrollBar);\n      window.removeEventListener('mouseup', this.releaseScrollBar);\n      window.removeEventListener('mousemove', this.moveScrollBar);\n      this.scrollbar.remove();\n    }\n  }, {\n    key: \"getScrollBar\",\n    value: function getScrollBar(e) {\n      this.isDraggingScrollbar = true;\n      this.checkScroll();\n      this.html.classList.remove(this.scrollingClass);\n      this.html.classList.add(this.draggingClass);\n    }\n  }, {\n    key: \"releaseScrollBar\",\n    value: function releaseScrollBar(e) {\n      this.isDraggingScrollbar = false;\n\n      if (this.isScrolling) {\n        this.html.classList.add(this.scrollingClass);\n      }\n\n      this.html.classList.remove(this.draggingClass);\n    }\n  }, {\n    key: \"moveScrollBar\",\n    value: function moveScrollBar(e) {\n      var _this5 = this;\n\n      if (this.isDraggingScrollbar) {\n        requestAnimationFrame(function () {\n          var x = (e.clientX - _this5.scrollbarBCR.left) * 100 / _this5.scrollbarWidth * _this5.instance.limit.x / 100;\n          var y = (e.clientY - _this5.scrollbarBCR.top) * 100 / _this5.scrollbarHeight * _this5.instance.limit.y / 100;\n\n          if (y > 0 && y < _this5.instance.limit.y) {\n            _this5.instance.delta.y = y;\n          }\n\n          if (x > 0 && x < _this5.instance.limit.x) {\n            _this5.instance.delta.x = x;\n          }\n        });\n      }\n    }\n  }, {\n    key: \"addElements\",\n    value: function addElements() {\n      var _this6 = this;\n\n      this.els = {};\n      this.parallaxElements = {}; // this.sections.forEach((section, y) => {\n\n      var els = this.el.querySelectorAll(\"[data-\".concat(this.name, \"]\"));\n      els.forEach(function (el, index) {\n        // Try and find the target's parent section\n        var targetParents = getParents(el);\n        var section = Object.entries(_this6.sections).map(function (_ref3) {\n          var _ref4 = _slicedToArray(_ref3, 2),\n              key = _ref4[0],\n              section = _ref4[1];\n\n          return section;\n        }).find(function (section) {\n          return targetParents.includes(section.el);\n        });\n        var cl = el.dataset[_this6.name + 'Class'] || _this6[\"class\"];\n        var id = typeof el.dataset[_this6.name + 'Id'] === 'string' ? el.dataset[_this6.name + 'Id'] : 'el' + index;\n        var top;\n        var left;\n        var repeat = el.dataset[_this6.name + 'Repeat'];\n        var call = el.dataset[_this6.name + 'Call'];\n        var position = el.dataset[_this6.name + 'Position'];\n        var delay = el.dataset[_this6.name + 'Delay'];\n        var direction = el.dataset[_this6.name + 'Direction'];\n        var sticky = typeof el.dataset[_this6.name + 'Sticky'] === 'string';\n        var speed = el.dataset[_this6.name + 'Speed'] ? parseFloat(el.dataset[_this6.name + 'Speed']) / 10 : false;\n        var offset = typeof el.dataset[_this6.name + 'Offset'] === 'string' ? el.dataset[_this6.name + 'Offset'].split(',') : _this6.offset;\n        var target = el.dataset[_this6.name + 'Target'];\n        var targetEl;\n\n        if (target !== undefined) {\n          targetEl = document.querySelector(\"\".concat(target));\n        } else {\n          targetEl = el;\n        }\n\n        var targetElBCR = targetEl.getBoundingClientRect();\n\n        if (section === null) {\n          top = targetElBCR.top + _this6.instance.scroll.y - getTranslate(targetEl).y;\n          left = targetElBCR.left + _this6.instance.scroll.x - getTranslate(targetEl).x;\n        } else {\n          if (!section.inView) {\n            top = targetElBCR.top - getTranslate(section.el).y - getTranslate(targetEl).y;\n            left = targetElBCR.left - getTranslate(section.el).x - getTranslate(targetEl).x;\n          } else {\n            top = targetElBCR.top + _this6.instance.scroll.y - getTranslate(targetEl).y;\n            left = targetElBCR.left + _this6.instance.scroll.x - getTranslate(targetEl).x;\n          }\n        }\n\n        var bottom = top + targetEl.offsetHeight;\n        var right = left + targetEl.offsetWidth;\n        var middle = {\n          x: (right - left) / 2 + left,\n          y: (bottom - top) / 2 + top\n        };\n\n        if (sticky) {\n          var elBCR = el.getBoundingClientRect();\n          var elTop = elBCR.top;\n          var elLeft = elBCR.left;\n          var elDistance = {\n            x: elLeft - left,\n            y: elTop - top\n          };\n          top += window.innerHeight;\n          left += window.innerWidth;\n          bottom = elTop + targetEl.offsetHeight - el.offsetHeight - elDistance[_this6.directionAxis];\n          right = elLeft + targetEl.offsetWidth - el.offsetWidth - elDistance[_this6.directionAxis];\n          middle = {\n            x: (right - left) / 2 + left,\n            y: (bottom - top) / 2 + top\n          };\n        }\n\n        if (repeat == 'false') {\n          repeat = false;\n        } else if (repeat != undefined) {\n          repeat = true;\n        } else {\n          repeat = _this6.repeat;\n        }\n\n        var relativeOffset = [0, 0];\n\n        if (offset) {\n          if (_this6.direction === 'horizontal') {\n            for (var i = 0; i < offset.length; i++) {\n              if (typeof offset[i] == 'string') {\n                if (offset[i].includes('%')) {\n                  relativeOffset[i] = parseInt(offset[i].replace('%', '') * _this6.windowWidth / 100);\n                } else {\n                  relativeOffset[i] = parseInt(offset[i]);\n                }\n              } else {\n                relativeOffset[i] = offset[i];\n              }\n            }\n\n            left = left + relativeOffset[0];\n            right = right - relativeOffset[1];\n          } else {\n            for (var i = 0; i < offset.length; i++) {\n              if (typeof offset[i] == 'string') {\n                if (offset[i].includes('%')) {\n                  relativeOffset[i] = parseInt(offset[i].replace('%', '') * _this6.windowHeight / 100);\n                } else {\n                  relativeOffset[i] = parseInt(offset[i]);\n                }\n              } else {\n                relativeOffset[i] = offset[i];\n              }\n            }\n\n            top = top + relativeOffset[0];\n            bottom = bottom - relativeOffset[1];\n          }\n        }\n\n        var mappedEl = {\n          el: el,\n          id: id,\n          \"class\": cl,\n          section: section,\n          top: top,\n          middle: middle,\n          bottom: bottom,\n          left: left,\n          right: right,\n          offset: offset,\n          progress: 0,\n          repeat: repeat,\n          inView: false,\n          call: call,\n          speed: speed,\n          delay: delay,\n          position: position,\n          target: targetEl,\n          direction: direction,\n          sticky: sticky\n        };\n        _this6.els[id] = mappedEl;\n\n        if (el.classList.contains(cl)) {\n          _this6.setInView(_this6.els[id], id);\n        }\n\n        if (speed !== false || sticky) {\n          _this6.parallaxElements[id] = mappedEl;\n        }\n      }); // });\n    }\n  }, {\n    key: \"addSections\",\n    value: function addSections() {\n      var _this7 = this;\n\n      this.sections = {};\n      var sections = this.el.querySelectorAll(\"[data-\".concat(this.name, \"-section]\"));\n\n      if (sections.length === 0) {\n        sections = [this.el];\n      }\n\n      sections.forEach(function (section, index) {\n        var id = typeof section.dataset[_this7.name + 'Id'] === 'string' ? section.dataset[_this7.name + 'Id'] : 'section' + index;\n        var sectionBCR = section.getBoundingClientRect();\n        var offset = {\n          x: sectionBCR.left - window.innerWidth * 1.5 - getTranslate(section).x,\n          y: sectionBCR.top - window.innerHeight * 1.5 - getTranslate(section).y\n        };\n        var limit = {\n          x: offset.x + sectionBCR.width + window.innerWidth * 2,\n          y: offset.y + sectionBCR.height + window.innerHeight * 2\n        };\n        var persistent = typeof section.dataset[_this7.name + 'Persistent'] === 'string';\n        section.setAttribute('data-scroll-section-id', id);\n        var mappedSection = {\n          el: section,\n          offset: offset,\n          limit: limit,\n          inView: false,\n          persistent: persistent,\n          id: id\n        };\n        _this7.sections[id] = mappedSection;\n      });\n    }\n  }, {\n    key: \"transform\",\n    value: function transform(element, x, y, delay) {\n      var transform;\n\n      if (!delay) {\n        transform = \"matrix3d(1,0,0.00,0,0.00,1,0.00,0,0,0,1,0,\".concat(x, \",\").concat(y, \",0,1)\");\n      } else {\n        var start = getTranslate(element);\n        var lerpX = lerp(start.x, x, delay);\n        var lerpY = lerp(start.y, y, delay);\n        transform = \"matrix3d(1,0,0.00,0,0.00,1,0.00,0,0,0,1,0,\".concat(lerpX, \",\").concat(lerpY, \",0,1)\");\n      }\n\n      element.style.webkitTransform = transform;\n      element.style.msTransform = transform;\n      element.style.transform = transform;\n    }\n  }, {\n    key: \"transformElements\",\n    value: function transformElements(isForced) {\n      var _this8 = this;\n\n      var setAllElements = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var scrollRight = this.instance.scroll.x + this.windowWidth;\n      var scrollBottom = this.instance.scroll.y + this.windowHeight;\n      var scrollMiddle = {\n        x: this.instance.scroll.x + this.windowMiddle.x,\n        y: this.instance.scroll.y + this.windowMiddle.y\n      };\n      Object.entries(this.parallaxElements).forEach(function (_ref5) {\n        var _ref6 = _slicedToArray(_ref5, 2),\n            i = _ref6[0],\n            current = _ref6[1];\n\n        var transformDistance = false;\n\n        if (isForced) {\n          transformDistance = 0;\n        }\n\n        if (current.inView || setAllElements) {\n          switch (current.position) {\n            case 'top':\n              transformDistance = _this8.instance.scroll[_this8.directionAxis] * -current.speed;\n              break;\n\n            case 'elementTop':\n              transformDistance = (scrollBottom - current.top) * -current.speed;\n              break;\n\n            case 'bottom':\n              transformDistance = (_this8.instance.limit[_this8.directionAxis] - scrollBottom + _this8.windowHeight) * current.speed;\n              break;\n\n            case 'left':\n              transformDistance = _this8.instance.scroll[_this8.directionAxis] * -current.speed;\n              break;\n\n            case 'elementLeft':\n              transformDistance = (scrollRight - current.left) * -current.speed;\n              break;\n\n            case 'right':\n              transformDistance = (_this8.instance.limit[_this8.directionAxis] - scrollRight + _this8.windowHeight) * current.speed;\n              break;\n\n            default:\n              transformDistance = (scrollMiddle[_this8.directionAxis] - current.middle[_this8.directionAxis]) * -current.speed;\n              break;\n          }\n        }\n\n        if (current.sticky) {\n          if (current.inView) {\n            if (_this8.direction === 'horizontal') {\n              transformDistance = _this8.instance.scroll.x - current.left + window.innerWidth;\n            } else {\n              transformDistance = _this8.instance.scroll.y - current.top + window.innerHeight;\n            }\n          } else {\n            if (_this8.direction === 'horizontal') {\n              if (_this8.instance.scroll.x < current.left - window.innerWidth && _this8.instance.scroll.x < current.left - window.innerWidth / 2) {\n                transformDistance = 0;\n              } else if (_this8.instance.scroll.x > current.right && _this8.instance.scroll.x > current.right + 100) {\n                transformDistance = current.right - current.left + window.innerWidth;\n              } else {\n                transformDistance = false;\n              }\n            } else {\n              if (_this8.instance.scroll.y < current.top - window.innerHeight && _this8.instance.scroll.y < current.top - window.innerHeight / 2) {\n                transformDistance = 0;\n              } else if (_this8.instance.scroll.y > current.bottom && _this8.instance.scroll.y > current.bottom + 100) {\n                transformDistance = current.bottom - current.top + window.innerHeight;\n              } else {\n                transformDistance = false;\n              }\n            }\n          }\n        }\n\n        if (transformDistance !== false) {\n          if (current.direction === 'horizontal' || _this8.direction === 'horizontal' && current.direction !== 'vertical') {\n            _this8.transform(current.el, transformDistance, 0, isForced ? false : current.delay);\n          } else {\n            _this8.transform(current.el, 0, transformDistance, isForced ? false : current.delay);\n          }\n        }\n      });\n    }\n    /**\n     * Scroll to a desired target.\n     *\n     * @param  Available options :\n     *          target {node, string, \"top\", \"bottom\", int} - The DOM element we want to scroll to\n     *          options {object} - Options object for additionnal settings.\n     * @return {void}\n     */\n\n  }, {\n    key: \"scrollTo\",\n    value: function scrollTo(target) {\n      var _this9 = this;\n\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      // Parse options\n      var offset = parseInt(options.offset) || 0; // An offset to apply on top of given `target` or `sourceElem`'s target\n\n      var duration = !isNaN(parseInt(options.duration)) ? parseInt(options.duration) : 1000; // Duration of the scroll animation in milliseconds\n\n      var easing = options.easing || [0.25, 0.0, 0.35, 1.0]; // An array of 4 floats between 0 and 1 defining the bezier curve for the animation's easing. See http://greweb.me/bezier-easing-editor/example/\n\n      var disableLerp = options.disableLerp ? true : false; // Lerp effect won't be applied if set to true\n\n      var callback = options.callback ? options.callback : false; // function called when scrollTo completes (note that it won't wait for lerp to stabilize)\n\n      easing = src$1.apply(void 0, _toConsumableArray(easing));\n\n      if (typeof target === 'string') {\n        // Selector or boundaries\n        if (target === 'top') {\n          target = 0;\n        } else if (target === 'bottom') {\n          target = this.instance.limit.y;\n        } else if (target === 'left') {\n          target = 0;\n        } else if (target === 'right') {\n          target = this.instance.limit.x;\n        } else {\n          target = document.querySelector(target); // If the query fails, abort\n\n          if (!target) {\n            return;\n          }\n        }\n      } else if (typeof target === 'number') {\n        // Absolute coordinate\n        target = parseInt(target);\n      } else if (target && target.tagName) ; else {\n        console.warn('`target` parameter is not valid');\n        return;\n      } // We have a target that is not a coordinate yet, get it\n\n\n      if (typeof target !== 'number') {\n        // Verify the given target belongs to this scroll scope\n        var targetInScope = getParents(target).includes(this.el);\n\n        if (!targetInScope) {\n          // If the target isn't inside our main element, abort any action\n          return;\n        } // Get target offset from top\n\n\n        var targetBCR = target.getBoundingClientRect();\n        var offsetTop = targetBCR.top;\n        var offsetLeft = targetBCR.left; // Try and find the target's parent section\n\n        var targetParents = getParents(target);\n        var parentSection = targetParents.find(function (candidate) {\n          return Object.entries(_this9.sections) // Get sections associative array as a regular array\n          .map(function (_ref7) {\n            var _ref8 = _slicedToArray(_ref7, 2),\n                key = _ref8[0],\n                section = _ref8[1];\n\n            return section;\n          }) // map to section only (we dont need the key here)\n          .find(function (section) {\n            return section.el == candidate;\n          }); // finally find the section that matches the candidate\n        });\n        var parentSectionOffset = 0;\n\n        if (parentSection) {\n          parentSectionOffset = getTranslate(parentSection)[this.directionAxis]; // We got a parent section, store it's current offset to remove it later\n        } else {\n          // if no parent section is found we need to use instance scroll directly\n          parentSectionOffset = -this.instance.scroll[this.directionAxis];\n        } // Final value of scroll destination : offsetTop + (optional offset given in options) - (parent's section translate)\n\n\n        if (this.direction === 'horizontal') {\n          offset = offsetLeft + offset - parentSectionOffset;\n        } else {\n          offset = offsetTop + offset - parentSectionOffset;\n        }\n      } else {\n        offset = target + offset;\n      } // Actual scrollto\n      // ==========================================================================\n      // Setup\n\n\n      var scrollStart = parseFloat(this.instance.delta[this.directionAxis]);\n      var scrollTarget = Math.max(0, Math.min(offset, this.instance.limit[this.directionAxis])); // Make sure our target is in the scroll boundaries\n\n      var scrollDiff = scrollTarget - scrollStart;\n\n      var render = function render(p) {\n        if (disableLerp) {\n          if (_this9.direction === 'horizontal') {\n            _this9.setScroll(scrollStart + scrollDiff * p, _this9.instance.delta.y);\n          } else {\n            _this9.setScroll(_this9.instance.delta.x, scrollStart + scrollDiff * p);\n          }\n        } else {\n          _this9.instance.delta[_this9.directionAxis] = scrollStart + scrollDiff * p;\n        }\n      }; // Prepare the scroll\n\n\n      this.animatingScroll = true; // This boolean allows to prevent `checkScroll()` from calling `stopScrolling` when the animation is slow (i.e. at the beginning of an EaseIn)\n\n      this.stopScrolling(); // Stop any movement, allows to kill any other `scrollTo` still happening\n\n      this.startScrolling(); // Restart the scroll\n      // Start the animation loop\n\n      var start = Date.now();\n\n      var loop = function loop() {\n        var p = (Date.now() - start) / duration; // Animation progress\n\n        if (p > 1) {\n          // Animation ends\n          render(1);\n          _this9.animatingScroll = false;\n          if (duration == 0) _this9.update();\n          if (callback) callback();\n        } else {\n          _this9.scrollToRaf = requestAnimationFrame(loop);\n          render(easing(p));\n        }\n      };\n\n      loop();\n    }\n  }, {\n    key: \"update\",\n    value: function update() {\n      this.setScrollLimit();\n      this.addSections();\n      this.addElements();\n      this.detectElements();\n      this.updateScroll();\n      this.transformElements(true);\n      this.reinitScrollBar();\n      this.checkScroll(true);\n    }\n  }, {\n    key: \"startScroll\",\n    value: function startScroll() {\n      this.stop = false;\n    }\n  }, {\n    key: \"stopScroll\",\n    value: function stopScroll() {\n      this.stop = true;\n    }\n  }, {\n    key: \"setScroll\",\n    value: function setScroll(x, y) {\n      this.instance = _objectSpread2(_objectSpread2({}, this.instance), {}, {\n        scroll: {\n          x: x,\n          y: y\n        },\n        delta: {\n          x: x,\n          y: y\n        },\n        speed: 0\n      });\n    }\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      _get(_getPrototypeOf(_default.prototype), \"destroy\", this).call(this);\n\n      this.stopScrolling();\n      this.html.classList.remove(this.smoothClass);\n      this.vs.destroy();\n      this.destroyScrollBar();\n      window.removeEventListener('keydown', this.checkKey, false);\n    }\n  }]);\n\n  return _default;\n}(_default);\n\nvar Smooth = /*#__PURE__*/function () {\n  function Smooth() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n    _classCallCheck(this, Smooth);\n\n    this.options = options; // Override default options with given ones\n\n    Object.assign(this, defaults, options);\n    this.smartphone = defaults.smartphone;\n    if (options.smartphone) Object.assign(this.smartphone, options.smartphone);\n    this.tablet = defaults.tablet;\n    if (options.tablet) Object.assign(this.tablet, options.tablet);\n    if (!this.smooth && this.direction == 'horizontal') console.warn('🚨 `smooth:false` & `horizontal` direction are not yet compatible');\n    if (!this.tablet.smooth && this.tablet.direction == 'horizontal') console.warn('🚨 `smooth:false` & `horizontal` direction are not yet compatible (tablet)');\n    if (!this.smartphone.smooth && this.smartphone.direction == 'horizontal') console.warn('🚨 `smooth:false` & `horizontal` direction are not yet compatible (smartphone)');\n    this.init();\n  }\n\n  _createClass(Smooth, [{\n    key: \"init\",\n    value: function init() {\n      this.options.isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1 || window.innerWidth < this.tablet.breakpoint;\n      this.options.isTablet = this.options.isMobile && window.innerWidth >= this.tablet.breakpoint;\n\n      if (this.smooth && !this.options.isMobile || this.tablet.smooth && this.options.isTablet || this.smartphone.smooth && this.options.isMobile && !this.options.isTablet) {\n        this.scroll = new _default$2(this.options);\n      } else {\n        this.scroll = new _default$1(this.options);\n      }\n\n      this.scroll.init();\n\n      if (window.location.hash) {\n        // Get the hash without the '#' and find the matching element\n        var id = window.location.hash.slice(1, window.location.hash.length);\n        var target = document.getElementById(id); // If found, scroll to the element\n\n        if (target) this.scroll.scrollTo(target);\n      }\n    }\n  }, {\n    key: \"update\",\n    value: function update() {\n      this.scroll.update();\n    }\n  }, {\n    key: \"start\",\n    value: function start() {\n      this.scroll.startScroll();\n    }\n  }, {\n    key: \"stop\",\n    value: function stop() {\n      this.scroll.stopScroll();\n    }\n  }, {\n    key: \"scrollTo\",\n    value: function scrollTo(target, options) {\n      this.scroll.scrollTo(target, options);\n    }\n  }, {\n    key: \"setScroll\",\n    value: function setScroll(x, y) {\n      this.scroll.setScroll(x, y);\n    }\n  }, {\n    key: \"on\",\n    value: function on(event, func) {\n      this.scroll.setEvents(event, func);\n    }\n  }, {\n    key: \"off\",\n    value: function off(event, func) {\n      this.scroll.unsetEvents(event, func);\n    }\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      this.scroll.destroy();\n    }\n  }]);\n\n  return Smooth;\n}();\n\nvar Native = /*#__PURE__*/function () {\n  function Native() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n    _classCallCheck(this, Native);\n\n    this.options = options; // Override default options with given ones\n\n    Object.assign(this, defaults, options);\n    this.smartphone = defaults.smartphone;\n    if (options.smartphone) Object.assign(this.smartphone, options.smartphone);\n    this.tablet = defaults.tablet;\n    if (options.tablet) Object.assign(this.tablet, options.tablet);\n    this.init();\n  }\n\n  _createClass(Native, [{\n    key: \"init\",\n    value: function init() {\n      this.scroll = new _default$1(this.options);\n      this.scroll.init();\n\n      if (window.location.hash) {\n        // Get the hash without the '#' and find the matching element\n        var id = window.location.hash.slice(1, window.location.hash.length);\n        var target = document.getElementById(id); // If found, scroll to the element\n\n        if (target) this.scroll.scrollTo(target);\n      }\n    }\n  }, {\n    key: \"update\",\n    value: function update() {\n      this.scroll.update();\n    }\n  }, {\n    key: \"start\",\n    value: function start() {\n      this.scroll.startScroll();\n    }\n  }, {\n    key: \"stop\",\n    value: function stop() {\n      this.scroll.stopScroll();\n    }\n  }, {\n    key: \"scrollTo\",\n    value: function scrollTo(target, options) {\n      this.scroll.scrollTo(target, options);\n    }\n  }, {\n    key: \"setScroll\",\n    value: function setScroll(x, y) {\n      this.scroll.setScroll(x, y);\n    }\n  }, {\n    key: \"on\",\n    value: function on(event, func) {\n      this.scroll.setEvents(event, func);\n    }\n  }, {\n    key: \"off\",\n    value: function off(event, func) {\n      this.scroll.unsetEvents(event, func);\n    }\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      this.scroll.destroy();\n    }\n  }]);\n\n  return Native;\n}();\n\nexport default Smooth;\nexport { Native, Smooth };\n"], "mappings": ";AACA,SAAS,gBAAgB,UAAU,aAAa;AAC9C,MAAI,EAAE,oBAAoB,cAAc;AACtC,UAAM,IAAI,UAAU,mCAAmC;AAAA,EACzD;AACF;AAEA,SAAS,kBAAkB,QAAQ,OAAO;AACxC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,aAAa,MAAM,CAAC;AACxB,eAAW,aAAa,WAAW,cAAc;AACjD,eAAW,eAAe;AAC1B,QAAI,WAAW,WAAY,YAAW,WAAW;AACjD,WAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,EAC1D;AACF;AAEA,SAAS,aAAa,aAAa,YAAY,aAAa;AAC1D,MAAI,WAAY,mBAAkB,YAAY,WAAW,UAAU;AACnE,MAAI,YAAa,mBAAkB,aAAa,WAAW;AAC3D,SAAO;AACT;AAEA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAE7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AACjD,QAAI,eAAgB,WAAU,QAAQ,OAAO,SAAU,KAAK;AAC1D,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IACtD,CAAC;AACD,SAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EAC/B;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAEpD,QAAI,IAAI,GAAG;AACT,cAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AACnD,wBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MAC1C,CAAC;AAAA,IACH,WAAW,OAAO,2BAA2B;AAC3C,aAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,IAC1E,OAAO;AACL,cAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC7C,eAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,MACjF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,UAAU,UAAU,YAAY;AACvC,MAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAC3D,UAAM,IAAI,UAAU,oDAAoD;AAAA,EAC1E;AAEA,WAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW;AAAA,IACrE,aAAa;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,MAAI,WAAY,iBAAgB,UAAU,UAAU;AACtD;AAEA,SAAS,gBAAgB,GAAG;AAC1B,oBAAkB,OAAO,iBAAiB,OAAO,iBAAiB,SAASA,iBAAgBC,IAAG;AAC5F,WAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,EAC/C;AACA,SAAO,gBAAgB,CAAC;AAC1B;AAEA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,oBAAkB,OAAO,kBAAkB,SAASC,iBAAgBD,IAAGE,IAAG;AACxE,IAAAF,GAAE,YAAYE;AACd,WAAOF;AAAA,EACT;AAEA,SAAO,gBAAgB,GAAG,CAAC;AAC7B;AAEA,SAAS,4BAA4B;AACnC,MAAI,OAAO,YAAY,eAAe,CAAC,QAAQ,UAAW,QAAO;AACjE,MAAI,QAAQ,UAAU,KAAM,QAAO;AACnC,MAAI,OAAO,UAAU,WAAY,QAAO;AAExC,MAAI;AACF,SAAK,UAAU,SAAS,KAAK,QAAQ,UAAU,MAAM,CAAC,GAAG,WAAY;AAAA,IAAC,CAAC,CAAC;AACxE,WAAO;AAAA,EACT,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AAEA,SAAS,uBAAuBG,OAAM;AACpC,MAAIA,UAAS,QAAQ;AACnB,UAAM,IAAI,eAAe,2DAA2D;AAAA,EACtF;AAEA,SAAOA;AACT;AAEA,SAAS,2BAA2BA,OAAM,MAAM;AAC9C,MAAI,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,aAAa;AACpE,WAAO;AAAA,EACT;AAEA,SAAO,uBAAuBA,KAAI;AACpC;AAEA,SAAS,aAAa,SAAS;AAC7B,MAAI,4BAA4B,0BAA0B;AAE1D,SAAO,SAAS,uBAAuB;AACrC,QAAI,QAAQ,gBAAgB,OAAO,GAC/B;AAEJ,QAAI,2BAA2B;AAC7B,UAAI,YAAY,gBAAgB,IAAI,EAAE;AAEtC,eAAS,QAAQ,UAAU,OAAO,WAAW,SAAS;AAAA,IACxD,OAAO;AACL,eAAS,MAAM,MAAM,MAAM,SAAS;AAAA,IACtC;AAEA,WAAO,2BAA2B,MAAM,MAAM;AAAA,EAChD;AACF;AAEA,SAAS,eAAe,QAAQ,UAAU;AACxC,SAAO,CAAC,OAAO,UAAU,eAAe,KAAK,QAAQ,QAAQ,GAAG;AAC9D,aAAS,gBAAgB,MAAM;AAC/B,QAAI,WAAW,KAAM;AAAA,EACvB;AAEA,SAAO;AACT;AAEA,SAAS,KAAK,QAAQ,UAAU,UAAU;AACxC,MAAI,OAAO,YAAY,eAAe,QAAQ,KAAK;AACjD,WAAO,QAAQ;AAAA,EACjB,OAAO;AACL,WAAO,SAASC,MAAKC,SAAQC,WAAUC,WAAU;AAC/C,UAAI,OAAO,eAAeF,SAAQC,SAAQ;AAE1C,UAAI,CAAC,KAAM;AACX,UAAI,OAAO,OAAO,yBAAyB,MAAMA,SAAQ;AAEzD,UAAI,KAAK,KAAK;AACZ,eAAO,KAAK,IAAI,KAAKC,SAAQ;AAAA,MAC/B;AAEA,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AAEA,SAAO,KAAK,QAAQ,UAAU,YAAY,MAAM;AAClD;AAEA,SAAS,eAAe,KAAK,GAAG;AAC9B,SAAO,gBAAgB,GAAG,KAAK,sBAAsB,KAAK,CAAC,KAAK,4BAA4B,KAAK,CAAC,KAAK,iBAAiB;AAC1H;AAEA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAmB;AACpH;AAEA,SAAS,mBAAmB,KAAK;AAC/B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO,kBAAkB,GAAG;AACtD;AAEA,SAAS,gBAAgB,KAAK;AAC5B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO;AACjC;AAEA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,IAAI,EAAG,QAAO,MAAM,KAAK,IAAI;AAC9F;AAEA,SAAS,sBAAsB,KAAK,GAAG;AACrC,MAAI,OAAO,WAAW,eAAe,EAAE,OAAO,YAAY,OAAO,GAAG,GAAI;AACxE,MAAI,OAAO,CAAC;AACZ,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AAET,MAAI;AACF,aAAS,KAAK,IAAI,OAAO,QAAQ,EAAE,GAAG,IAAI,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,MAAM;AAClF,WAAK,KAAK,GAAG,KAAK;AAElB,UAAI,KAAK,KAAK,WAAW,EAAG;AAAA,IAC9B;AAAA,EACF,SAAS,KAAK;AACZ,SAAK;AACL,SAAK;AAAA,EACP,UAAE;AACA,QAAI;AACF,UAAI,CAAC,MAAM,GAAG,QAAQ,KAAK,KAAM,IAAG,QAAQ,EAAE;AAAA,IAChD,UAAE;AACA,UAAI,GAAI,OAAM;AAAA,IAChB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC,EAAG;AACR,MAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AACjH;AAEA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAE/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AAEpE,SAAO;AACT;AAEA,SAAS,qBAAqB;AAC5B,QAAM,IAAI,UAAU,sIAAsI;AAC5J;AAEA,SAAS,mBAAmB;AAC1B,QAAM,IAAI,UAAU,2IAA2I;AACjK;AAEA,IAAI,WAAW;AAAA,EACb,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,QAAQ,CAAC,GAAG,CAAC;AAAA,EACb,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,cAAc;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAAA,EACA,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,MAAM;AAAA,EACN,SAAS;AAAA,EACT,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,aAAa;AAAA,EACb,WAAW;AAAA,EACX,UAAU;AAAA,EACV,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,QAAQ;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,kBAAkB;AAAA,IAClB,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,kBAAkB;AAAA,EACpB;AACF;AAEA,IAAI,WAAwB,WAAY;AACtC,WAASC,YAAW;AAClB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEnF,oBAAgB,MAAMA,SAAQ;AAE9B,WAAO,OAAO,MAAM,UAAU,OAAO;AACrC,SAAK,aAAa,SAAS;AAC3B,QAAI,QAAQ,WAAY,QAAO,OAAO,KAAK,YAAY,QAAQ,UAAU;AACzE,SAAK,SAAS,SAAS;AACvB,QAAI,QAAQ,OAAQ,QAAO,OAAO,KAAK,QAAQ,QAAQ,MAAM;AAC7D,SAAK,YAAY;AACjB,SAAK,OAAO,SAAS;AACrB,SAAK,eAAe,OAAO;AAC3B,SAAK,cAAc,OAAO;AAC1B,SAAK,eAAe;AAAA,MAClB,GAAG,KAAK,cAAc;AAAA,MACtB,GAAG,KAAK,eAAe;AAAA,IACzB;AACA,SAAK,MAAM,CAAC;AACZ,SAAK,kBAAkB,CAAC;AACxB,SAAK,YAAY,CAAC;AAClB,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AACvB,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,WAAW;AAAA,MACd,QAAQ;AAAA,QACN,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,MACA,OAAO;AAAA,QACL,GAAG,KAAK,KAAK;AAAA,QACb,GAAG,KAAK,KAAK;AAAA,MACf;AAAA,MACA,iBAAiB,KAAK;AAAA,IACxB;AAEA,QAAI,KAAK,UAAU;AACjB,UAAI,KAAK,UAAU;AACjB,aAAK,UAAU;AAAA,MACjB,OAAO;AACL,aAAK,UAAU;AAAA,MACjB;AAAA,IACF,OAAO;AACL,WAAK,UAAU;AAAA,IACjB;AAEA,QAAI,KAAK,SAAU,MAAK,YAAY,KAAK,KAAK,OAAO,EAAE;AAEvD,QAAI,KAAK,cAAc,cAAc;AACnC,WAAK,gBAAgB;AAAA,IACvB,OAAO;AACL,WAAK,gBAAgB;AAAA,IACvB;AAEA,QAAI,KAAK,cAAc;AACrB,WAAK,SAAS,YAAY;AAAA,IAC5B;AAEA,QAAI,KAAK,cAAc;AACrB,WAAK,SAAS,QAAQ;AAAA,IACxB;AAEA,SAAK,KAAK,UAAU,IAAI,KAAK,SAAS;AACtC,WAAO,iBAAiB,UAAU,KAAK,aAAa,KAAK;AAAA,EAC3D;AAEA,eAAaA,WAAU,CAAC;AAAA,IACtB,KAAK;AAAA,IACL,OAAO,SAAS,OAAO;AACrB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,WAAK,eAAe;AAAA,IACtB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,UAAI,QAAQ;AAEZ,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,aAAa;AAClB,8BAAsB,WAAY;AAChC,gBAAM,OAAO;AAEb,gBAAM,aAAa;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AAAA,IAAC;AAAA,EAC5B,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe;AAC7B,UAAI,CAAC,KAAK,sBAAuB;AACjC,WAAK,WAAW,2DAA2D,KAAK,UAAU,SAAS,KAAK,UAAU,aAAa,cAAc,UAAU,iBAAiB,KAAK,KAAK,cAAc,KAAK,OAAO;AAC5M,WAAK,WAAW,KAAK,YAAY,KAAK,eAAe,KAAK,OAAO;AACjE,UAAI,aAAa,KAAK;AAEtB,UAAI,KAAK,UAAU;AACjB,YAAI,KAAK,UAAU;AACjB,eAAK,UAAU;AAAA,QACjB,OAAO;AACL,eAAK,UAAU;AAAA,QACjB;AAAA,MACF,OAAO;AACL,aAAK,UAAU;AAAA,MACjB;AAEA,UAAI,cAAc,KAAK,SAAS;AAC9B,YAAI,YAAY,cAAc,YAAY,KAAK,SAAS,KAAK,UAAU,EAAE;AACzE,YAAI,YAAY,KAAK,WAAW,YAAY,KAAK,SAAS,KAAK,KAAK,OAAO,EAAE;AAC7E,YAAI,aAAa,UAAW,QAAO,SAAS,OAAO;AAAA,MACrD;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa;AAC3B,UAAI,SAAS;AAEb,WAAK,cAAc,KAAK,GAAG,iBAAiB,SAAS,OAAO,KAAK,MAAM,MAAM,CAAC;AAC9E,WAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,WAAK,YAAY,QAAQ,SAAU,IAAI;AACrC,WAAG,iBAAiB,SAAS,OAAO,aAAa,KAAK;AAAA,MACxD,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,OAAO;AACjC,YAAM,eAAe;AACrB,WAAK,SAAS,MAAM,cAAc,aAAa,QAAQ,OAAO,KAAK,MAAM,OAAO,CAAC,KAAK,MAAM,cAAc,aAAa,MAAM,GAAG;AAAA,QAC9H,QAAQ,MAAM,cAAc,aAAa,QAAQ,OAAO,KAAK,MAAM,SAAS,CAAC;AAAA,MAC/E,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAAA,IAAC;AAAA,EACjC,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe,iBAAiB;AAC9C,UAAI,SAAS;AAEb,UAAI,YAAY,KAAK,SAAS,OAAO;AACrC,UAAI,eAAe,YAAY,KAAK;AACpC,UAAI,aAAa,KAAK,SAAS,OAAO;AACtC,UAAI,cAAc,aAAa,KAAK;AACpC,aAAO,QAAQ,KAAK,GAAG,EAAE,QAAQ,SAAU,MAAM;AAC/C,YAAI,QAAQ,eAAe,MAAM,CAAC,GAC9B,IAAI,MAAM,CAAC,GACX,KAAK,MAAM,CAAC;AAEhB,YAAI,OAAO,CAAC,GAAG,UAAU,kBAAkB;AACzC,cAAI,OAAO,cAAc,cAAc;AACrC,gBAAI,eAAe,GAAG,QAAQ,aAAa,GAAG,OAAO;AACnD,qBAAO,UAAU,IAAI,CAAC;AAAA,YACxB;AAAA,UACF,OAAO;AACL,gBAAI,gBAAgB,GAAG,OAAO,YAAY,GAAG,QAAQ;AACnD,qBAAO,UAAU,IAAI,CAAC;AAAA,YACxB;AAAA,UACF;AAAA,QACF;AAEA,YAAI,MAAM,GAAG,QAAQ;AACnB,cAAI,OAAO,cAAc,cAAc;AACrC,gBAAI,QAAQ,GAAG,QAAQ,GAAG;AAC1B,eAAG,YAAY,OAAO,SAAS,OAAO,KAAK,GAAG,OAAO,OAAO,iBAAiB,QAAQ,OAAO;AAE5F,gBAAI,cAAc,GAAG,QAAQ,aAAa,GAAG,OAAO;AAClD,qBAAO,aAAa,IAAI,CAAC;AAAA,YAC3B;AAAA,UACF,OAAO;AACL,gBAAI,SAAS,GAAG,SAAS,GAAG;AAC5B,eAAG,YAAY,OAAO,SAAS,OAAO,KAAK,GAAG,MAAM,OAAO,kBAAkB,SAAS,OAAO;AAE7F,gBAAI,eAAe,GAAG,OAAO,YAAY,GAAG,QAAQ;AAClD,qBAAO,aAAa,IAAI,CAAC;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAID,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,SAAS,GAAG;AACpC,WAAK,IAAI,CAAC,EAAE,SAAS;AACrB,cAAQ,GAAG,UAAU,IAAI,QAAQ,OAAO,CAAC;AACzC,WAAK,gBAAgB,CAAC,IAAI;AAE1B,UAAI,QAAQ,QAAQ,KAAK,iBAAiB;AACxC,aAAK,aAAa,SAAS,OAAO;AAElC,YAAI,CAAC,QAAQ,QAAQ;AACnB,eAAK,IAAI,CAAC,EAAE,OAAO;AAAA,QACrB;AAAA,MACF;AAAA,IAMF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa,SAAS,GAAG;AACvC,UAAI,SAAS;AAGb,WAAK,IAAI,CAAC,EAAE,SAAS;AAErB,aAAO,KAAK,KAAK,eAAe,EAAE,QAAQ,SAAU,IAAI;AACtD,eAAO,KAAK,OAAO,OAAO,gBAAgB,EAAE;AAAA,MAC9C,CAAC;AAED,UAAI,QAAQ,QAAQ,KAAK,iBAAiB;AACxC,aAAK,aAAa,SAAS,MAAM;AAAA,MACnC;AAEA,UAAI,QAAQ,QAAQ;AAClB,gBAAQ,GAAG,UAAU,OAAO,QAAQ,OAAO,CAAC;AAAA,MAC9C;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa,SAAS,KAAK;AACzC,WAAK,UAAU;AACf,WAAK,YAAY,QAAQ,KAAK,MAAM,GAAG,EAAE,IAAI,SAAU,MAAM;AAC3D,eAAO,KAAK,KAAK;AAAA,MACnB,CAAC;AACD,WAAK,UAAU;AACf,UAAI,KAAK,UAAU,UAAU,EAAG,MAAK,YAAY,KAAK,UAAU,CAAC;AACjE,UAAI,YAAY,IAAI,MAAM,KAAK,YAAY,MAAM;AACjD,WAAK,GAAG,cAAc,SAAS;AAAA,IACjC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB;AAC/B,UAAI,cAAc,IAAI,MAAM,KAAK,YAAY,QAAQ;AACrD,WAAK,GAAG,cAAc,WAAW;AAAA,IACnC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,OAAO,MAAM;AACrC,UAAI,CAAC,KAAK,UAAU,KAAK,GAAG;AAC1B,aAAK,UAAU,KAAK,IAAI,CAAC;AAAA,MAC3B;AAEA,UAAI,OAAO,KAAK,UAAU,KAAK;AAC/B,WAAK,KAAK,IAAI;AAEd,UAAI,KAAK,WAAW,GAAG;AACrB,aAAK,GAAG,iBAAiB,KAAK,YAAY,OAAO,KAAK,YAAY,KAAK;AAAA,MACzE;AAEA,UAAI,UAAU,QAAQ;AACpB,aAAK,kBAAkB;AACvB,aAAK,eAAe,IAAI;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,OAAO,MAAM;AACvC,UAAI,CAAC,KAAK,UAAU,KAAK,EAAG;AAC5B,UAAI,OAAO,KAAK,UAAU,KAAK;AAC/B,UAAI,QAAQ,KAAK,QAAQ,IAAI;AAC7B,UAAI,QAAQ,EAAG;AACf,WAAK,OAAO,OAAO,CAAC;AAEpB,UAAI,KAAK,UAAU,GAAG;AACpB,aAAK,GAAG,oBAAoB,KAAK,YAAY,OAAO,KAAK,YAAY,KAAK;AAAA,MAC5E;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW,OAAO;AAChC,UAAI,SAAS;AAEb,UAAI,OAAO,MAAM,KAAK,QAAQ,KAAK,WAAW,EAAE;AAChD,UAAI,OAAO,KAAK,UAAU,IAAI;AAC9B,UAAI,CAAC,QAAQ,KAAK,WAAW,EAAG;AAChC,WAAK,QAAQ,SAAU,MAAM;AAC3B,gBAAQ,MAAM;AAAA,UACZ,KAAK;AACH,mBAAO,KAAK,OAAO,QAAQ;AAAA,UAE7B,KAAK;AACH,mBAAO,KAAK,OAAO,WAAW,OAAO,SAAS,OAAO,OAAO;AAAA,UAE9D;AACE,mBAAO,KAAK;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAAA,IAAC;AAAA,EACjC,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa;AAAA,IAAC;AAAA,EAChC,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,GAAG,GAAG;AAC9B,WAAK,SAAS,SAAS;AAAA,QACrB,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACxB,UAAI,SAAS;AAEb,aAAO,oBAAoB,UAAU,KAAK,aAAa,KAAK;AAC5D,aAAO,KAAK,KAAK,SAAS,EAAE,QAAQ,SAAU,OAAO;AACnD,eAAO,GAAG,oBAAoB,OAAO,YAAY,OAAO,OAAO,YAAY,KAAK;AAAA,MAClF,CAAC;AACD,WAAK,YAAY,CAAC;AAClB,WAAK,YAAY,QAAQ,SAAU,IAAI;AACrC,WAAG,oBAAoB,SAAS,OAAO,aAAa,KAAK;AAAA,MAC3D,CAAC;AACD,WAAK,KAAK,UAAU,OAAO,KAAK,SAAS;AAAA,IAC3C;AAAA,EACF,CAAC,CAAC;AAEF,SAAOA;AACT,EAAE;AAEF,IAAI,iBAAiB,OAAO,eAAe,cAAc,aAAa,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAC;AAE9L,SAAS,qBAAqB,IAAI,QAAQ;AACzC,SAAO,SAAS,EAAE,SAAS,CAAC,EAAE,GAAG,GAAG,QAAQ,OAAO,OAAO,GAAG,OAAO;AACrE;AAEA,IAAI,eAAe,qBAAqB,SAAU,QAAQ,SAAS;AAEnE,GAAC,WAAY;AAGX,aAAS,WAAW;AAElB,UAAI,IAAI;AACR,UAAI,IAAI;AAGR,UACE,oBAAoB,EAAE,gBAAgB,SACtC,EAAE,kCAAkC,MACpC;AACA;AAAA,MACF;AAGA,UAAI,UAAU,EAAE,eAAe,EAAE;AACjC,UAAI,cAAc;AAGlB,UAAI,WAAW;AAAA,QACb,QAAQ,EAAE,UAAU,EAAE;AAAA,QACtB,UAAU,EAAE;AAAA,QACZ,eAAe,QAAQ,UAAU,UAAU;AAAA,QAC3C,gBAAgB,QAAQ,UAAU;AAAA,MACpC;AAGA,UAAI,MACF,EAAE,eAAe,EAAE,YAAY,MAC3B,EAAE,YAAY,IAAI,KAAK,EAAE,WAAW,IACpC,KAAK;AAQX,eAAS,mBAAmB,WAAW;AACrC,YAAI,oBAAoB,CAAC,SAAS,YAAY,OAAO;AAErD,eAAO,IAAI,OAAO,kBAAkB,KAAK,GAAG,CAAC,EAAE,KAAK,SAAS;AAAA,MAC/D;AAOA,UAAI,qBAAqB,mBAAmB,EAAE,UAAU,SAAS,IAAI,IAAI;AASzE,eAAS,cAAc,GAAG,GAAG;AAC3B,aAAK,aAAa;AAClB,aAAK,YAAY;AAAA,MACnB;AAQA,eAAS,KAAK,GAAG;AACf,eAAO,OAAO,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,MACxC;AAQA,eAAS,cAAc,UAAU;AAC/B,YACE,aAAa,QACb,OAAO,aAAa,YACpB,SAAS,aAAa,UACtB,SAAS,aAAa,UACtB,SAAS,aAAa,WACtB;AAGA,iBAAO;AAAA,QACT;AAEA,YAAI,OAAO,aAAa,YAAY,SAAS,aAAa,UAAU;AAElE,iBAAO;AAAA,QACT;AAGA,cAAM,IAAI;AAAA,UACR,sCACE,SAAS,WACT;AAAA,QACJ;AAAA,MACF;AASA,eAAS,mBAAmB,IAAI,MAAM;AACpC,YAAI,SAAS,KAAK;AAChB,iBAAO,GAAG,eAAe,qBAAqB,GAAG;AAAA,QACnD;AAEA,YAAI,SAAS,KAAK;AAChB,iBAAO,GAAG,cAAc,qBAAqB,GAAG;AAAA,QAClD;AAAA,MACF;AASA,eAAS,YAAY,IAAI,MAAM;AAC7B,YAAI,gBAAgB,EAAE,iBAAiB,IAAI,IAAI,EAAE,aAAa,IAAI;AAElE,eAAO,kBAAkB,UAAU,kBAAkB;AAAA,MACvD;AASA,eAAS,aAAa,IAAI;AACxB,YAAI,gBAAgB,mBAAmB,IAAI,GAAG,KAAK,YAAY,IAAI,GAAG;AACtE,YAAI,gBAAgB,mBAAmB,IAAI,GAAG,KAAK,YAAY,IAAI,GAAG;AAEtE,eAAO,iBAAiB;AAAA,MAC1B;AAQA,eAAS,qBAAqB,IAAI;AAChC,eAAO,OAAO,EAAE,QAAQ,aAAa,EAAE,MAAM,OAAO;AAClD,eAAK,GAAG,cAAc,GAAG;AAAA,QAC3B;AAEA,eAAO;AAAA,MACT;AAQA,eAAS,KAAK,SAAS;AACrB,YAAI,OAAO,IAAI;AACf,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI,WAAW,OAAO,QAAQ,aAAa;AAG3C,kBAAU,UAAU,IAAI,IAAI;AAG5B,gBAAQ,KAAK,OAAO;AAEpB,mBAAW,QAAQ,UAAU,QAAQ,IAAI,QAAQ,UAAU;AAC3D,mBAAW,QAAQ,UAAU,QAAQ,IAAI,QAAQ,UAAU;AAE3D,gBAAQ,OAAO,KAAK,QAAQ,YAAY,UAAU,QAAQ;AAG1D,YAAI,aAAa,QAAQ,KAAK,aAAa,QAAQ,GAAG;AACpD,YAAE,sBAAsB,KAAK,KAAK,GAAG,OAAO,CAAC;AAAA,QAC/C;AAAA,MACF;AAUA,eAAS,aAAa,IAAI,GAAG,GAAG;AAC9B,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI,YAAY,IAAI;AAGpB,YAAI,OAAO,EAAE,MAAM;AACjB,uBAAa;AACb,mBAAS,EAAE,WAAW,EAAE;AACxB,mBAAS,EAAE,WAAW,EAAE;AACxB,mBAAS,SAAS;AAAA,QACpB,OAAO;AACL,uBAAa;AACb,mBAAS,GAAG;AACZ,mBAAS,GAAG;AACZ,mBAAS;AAAA,QACX;AAGA,aAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAIA,QAAE,SAAS,EAAE,WAAW,WAAW;AAEjC,YAAI,UAAU,CAAC,MAAM,QAAW;AAC9B;AAAA,QACF;AAGA,YAAI,cAAc,UAAU,CAAC,CAAC,MAAM,MAAM;AACxC,mBAAS,OAAO;AAAA,YACd;AAAA,YACA,UAAU,CAAC,EAAE,SAAS,SAClB,UAAU,CAAC,EAAE,OACb,OAAO,UAAU,CAAC,MAAM,WACtB,UAAU,CAAC,IACX,EAAE,WAAW,EAAE;AAAA;AAAA,YAErB,UAAU,CAAC,EAAE,QAAQ,SACjB,UAAU,CAAC,EAAE,MACb,UAAU,CAAC,MAAM,SACf,UAAU,CAAC,IACX,EAAE,WAAW,EAAE;AAAA,UACvB;AAEA;AAAA,QACF;AAGA,qBAAa;AAAA,UACX;AAAA,UACA,EAAE;AAAA,UACF,UAAU,CAAC,EAAE,SAAS,SAClB,CAAC,CAAC,UAAU,CAAC,EAAE,OACf,EAAE,WAAW,EAAE;AAAA,UACnB,UAAU,CAAC,EAAE,QAAQ,SACjB,CAAC,CAAC,UAAU,CAAC,EAAE,MACf,EAAE,WAAW,EAAE;AAAA,QACrB;AAAA,MACF;AAGA,QAAE,WAAW,WAAW;AAEtB,YAAI,UAAU,CAAC,MAAM,QAAW;AAC9B;AAAA,QACF;AAGA,YAAI,cAAc,UAAU,CAAC,CAAC,GAAG;AAC/B,mBAAS,SAAS;AAAA,YAChB;AAAA,YACA,UAAU,CAAC,EAAE,SAAS,SAClB,UAAU,CAAC,EAAE,OACb,OAAO,UAAU,CAAC,MAAM,WAAW,UAAU,CAAC,IAAI;AAAA,YACtD,UAAU,CAAC,EAAE,QAAQ,SACjB,UAAU,CAAC,EAAE,MACb,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,UAClD;AAEA;AAAA,QACF;AAGA,qBAAa;AAAA,UACX;AAAA,UACA,EAAE;AAAA,UACF,CAAC,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;AAAA,UACtC,CAAC,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE;AAAA,QACvC;AAAA,MACF;AAGA,cAAQ,UAAU,SAAS,QAAQ,UAAU,WAAW,WAAW;AAEjE,YAAI,UAAU,CAAC,MAAM,QAAW;AAC9B;AAAA,QACF;AAGA,YAAI,cAAc,UAAU,CAAC,CAAC,MAAM,MAAM;AAExC,cAAI,OAAO,UAAU,CAAC,MAAM,YAAY,UAAU,CAAC,MAAM,QAAW;AAClE,kBAAM,IAAI,YAAY,8BAA8B;AAAA,UACtD;AAEA,mBAAS,cAAc;AAAA,YACrB;AAAA;AAAA,YAEA,UAAU,CAAC,EAAE,SAAS,SAClB,CAAC,CAAC,UAAU,CAAC,EAAE,OACf,OAAO,UAAU,CAAC,MAAM,WAAW,CAAC,CAAC,UAAU,CAAC,IAAI,KAAK;AAAA;AAAA,YAE7D,UAAU,CAAC,EAAE,QAAQ,SACjB,CAAC,CAAC,UAAU,CAAC,EAAE,MACf,UAAU,CAAC,MAAM,SAAY,CAAC,CAAC,UAAU,CAAC,IAAI,KAAK;AAAA,UACzD;AAEA;AAAA,QACF;AAEA,YAAI,OAAO,UAAU,CAAC,EAAE;AACxB,YAAI,MAAM,UAAU,CAAC,EAAE;AAGvB,qBAAa;AAAA,UACX;AAAA,UACA;AAAA,UACA,OAAO,SAAS,cAAc,KAAK,aAAa,CAAC,CAAC;AAAA,UAClD,OAAO,QAAQ,cAAc,KAAK,YAAY,CAAC,CAAC;AAAA,QAClD;AAAA,MACF;AAGA,cAAQ,UAAU,WAAW,WAAW;AAEtC,YAAI,UAAU,CAAC,MAAM,QAAW;AAC9B;AAAA,QACF;AAGA,YAAI,cAAc,UAAU,CAAC,CAAC,MAAM,MAAM;AACxC,mBAAS,cAAc;AAAA,YACrB;AAAA,YACA,UAAU,CAAC,EAAE,SAAS,SAClB,CAAC,CAAC,UAAU,CAAC,EAAE,OAAO,KAAK,aAC3B,CAAC,CAAC,UAAU,CAAC,IAAI,KAAK;AAAA,YAC1B,UAAU,CAAC,EAAE,QAAQ,SACjB,CAAC,CAAC,UAAU,CAAC,EAAE,MAAM,KAAK,YAC1B,CAAC,CAAC,UAAU,CAAC,IAAI,KAAK;AAAA,UAC5B;AAEA;AAAA,QACF;AAEA,aAAK,OAAO;AAAA,UACV,MAAM,CAAC,CAAC,UAAU,CAAC,EAAE,OAAO,KAAK;AAAA,UACjC,KAAK,CAAC,CAAC,UAAU,CAAC,EAAE,MAAM,KAAK;AAAA,UAC/B,UAAU,UAAU,CAAC,EAAE;AAAA,QACzB,CAAC;AAAA,MACH;AAGA,cAAQ,UAAU,iBAAiB,WAAW;AAE5C,YAAI,cAAc,UAAU,CAAC,CAAC,MAAM,MAAM;AACxC,mBAAS,eAAe;AAAA,YACtB;AAAA,YACA,UAAU,CAAC,MAAM,SAAY,OAAO,UAAU,CAAC;AAAA,UACjD;AAEA;AAAA,QACF;AAGA,YAAI,mBAAmB,qBAAqB,IAAI;AAChD,YAAI,cAAc,iBAAiB,sBAAsB;AACzD,YAAI,cAAc,KAAK,sBAAsB;AAE7C,YAAI,qBAAqB,EAAE,MAAM;AAE/B,uBAAa;AAAA,YACX;AAAA,YACA;AAAA,YACA,iBAAiB,aAAa,YAAY,OAAO,YAAY;AAAA,YAC7D,iBAAiB,YAAY,YAAY,MAAM,YAAY;AAAA,UAC7D;AAGA,cAAI,EAAE,iBAAiB,gBAAgB,EAAE,aAAa,SAAS;AAC7D,cAAE,SAAS;AAAA,cACT,MAAM,YAAY;AAAA,cAClB,KAAK,YAAY;AAAA,cACjB,UAAU;AAAA,YACZ,CAAC;AAAA,UACH;AAAA,QACF,OAAO;AAEL,YAAE,SAAS;AAAA,YACT,MAAM,YAAY;AAAA,YAClB,KAAK,YAAY;AAAA,YACjB,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAEA;AAEE,aAAO,UAAU,EAAE,SAAmB;AAAA,IACxC;AAAA,EAEF,GAAE;AACF,CAAC;AACD,IAAI,iBAAiB,aAAa;AAElC,IAAI,aAA0B,SAAU,OAAO;AAC7C,YAAUA,WAAU,KAAK;AAEzB,MAAI,SAAS,aAAaA,SAAQ;AAElC,WAASA,YAAW;AAClB,QAAI;AAEJ,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEnF,oBAAgB,MAAMA,SAAQ;AAE9B,YAAQ,OAAO,KAAK,MAAM,OAAO;AAEjC,QAAI,MAAM,mBAAmB;AAC3B,UAAI,QAAQ,mBAAmB;AAC7B,gBAAQ,oBAAoB;AAAA,MAC9B;AAEA,aAAO,SAAS,GAAG,CAAC;AAAA,IACtB;AAEA,WAAO,iBAAiB,UAAU,MAAM,aAAa,KAAK;AAE1D,QAAI,OAAO,yBAAyB,QAAW;AAC7C,aAAO,uBAAuB;AAC9B,aAAO,qBAAqB,SAAS;AAAA,IACvC;AAEA,WAAO;AAAA,EACT;AAEA,eAAaA,WAAU,CAAC;AAAA,IACtB,KAAK;AAAA,IACL,OAAO,SAAS,OAAO;AACrB,WAAK,SAAS,OAAO,IAAI,OAAO;AAChC,WAAK,YAAY;AACjB,WAAK,eAAe;AAEpB,WAAK,gBAAgBA,UAAS,SAAS,GAAG,QAAQ,IAAI,EAAE,KAAK,IAAI;AAAA,IACnE;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,UAAI,SAAS;AAEb,WAAK,gBAAgBA,UAAS,SAAS,GAAG,eAAe,IAAI,EAAE,KAAK,IAAI;AAExE,UAAI,KAAK,cAAc;AACrB,aAAK,aAAa;AAAA,MACpB;AAEA,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS;AACd,aAAK,UAAU,KAAK,IAAI;AAAA,MAC1B;AAEA,WAAK,SAAS,OAAO,IAAI,OAAO;AAEhC,UAAI,OAAO,QAAQ,KAAK,GAAG,EAAE,QAAQ;AACnC,YAAI,CAAC,KAAK,kBAAkB;AAC1B,gCAAsB,WAAY;AAChC,mBAAO,eAAe;AAAA,UACxB,CAAC;AACD,eAAK,mBAAmB;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe;AAC7B,UAAI,OAAO,cAAc,KAAK,SAAS,OAAO,GAAG;AAC/C,YAAI,KAAK,SAAS,cAAc,QAAQ;AACtC,eAAK,SAAS,YAAY;AAAA,QAC5B;AAAA,MACF,WAAW,OAAO,cAAc,KAAK,SAAS,OAAO,GAAG;AACtD,YAAI,KAAK,SAAS,cAAc,MAAM;AACpC,eAAK,SAAS,YAAY;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW;AACzB,UAAI,OAAO,eAAe,KAAK,SAAS,OAAO,GAAG;AAChD,aAAK,SAAS,SAAS,OAAO,cAAc,KAAK,SAAS,OAAO,KAAK,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,OAAO;AAAA,MAC7G,OAAO;AACL,aAAK,SAAS,QAAQ;AAAA,MACxB;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,UAAI,OAAO,QAAQ,KAAK,GAAG,EAAE,QAAQ;AACnC,aAAK,eAAe,OAAO;AAC3B,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,UAAI,SAAS;AAEb,WAAK,MAAM,CAAC;AACZ,UAAI,MAAM,KAAK,GAAG,iBAAiB,WAAW,KAAK,OAAO,GAAG;AAC7D,UAAI,QAAQ,SAAU,IAAI,OAAO;AAC/B,YAAI,MAAM,GAAG,sBAAsB;AACnC,YAAI,KAAK,GAAG,QAAQ,OAAO,OAAO,OAAO,KAAK,OAAO,OAAO;AAC5D,YAAI,KAAK,OAAO,GAAG,QAAQ,OAAO,OAAO,IAAI,MAAM,WAAW,GAAG,QAAQ,OAAO,OAAO,IAAI,IAAI;AAC/F,YAAI;AACJ,YAAI;AACJ,YAAI,SAAS,OAAO,GAAG,QAAQ,OAAO,OAAO,QAAQ,MAAM,WAAW,GAAG,QAAQ,OAAO,OAAO,QAAQ,EAAE,MAAM,GAAG,IAAI,OAAO;AAC7H,YAAI,SAAS,GAAG,QAAQ,OAAO,OAAO,QAAQ;AAC9C,YAAI,OAAO,GAAG,QAAQ,OAAO,OAAO,MAAM;AAC1C,YAAI,SAAS,GAAG,QAAQ,OAAO,OAAO,QAAQ;AAC9C,YAAI;AAEJ,YAAI,WAAW,QAAW;AACxB,qBAAW,SAAS,cAAc,GAAG,OAAO,MAAM,CAAC;AAAA,QACrD,OAAO;AACL,qBAAW;AAAA,QACb;AAEA,YAAI,cAAc,SAAS,sBAAsB;AACjD,cAAM,YAAY,MAAM,OAAO,SAAS,OAAO;AAC/C,eAAO,YAAY,OAAO,OAAO,SAAS,OAAO;AACjD,YAAI,SAAS,MAAM,SAAS;AAC5B,YAAI,QAAQ,OAAO,SAAS;AAE5B,YAAI,UAAU,SAAS;AACrB,mBAAS;AAAA,QACX,WAAW,UAAU,QAAW;AAC9B,mBAAS;AAAA,QACX,OAAO;AACL,mBAAS,OAAO;AAAA,QAClB;AAEA,YAAI,iBAAiB,OAAO,kBAAkB,MAAM;AAEpD,cAAM,MAAM,eAAe,CAAC;AAC5B,iBAAS,SAAS,eAAe,CAAC;AAClC,YAAI,WAAW;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU;AAAA,UACV;AAAA,UACA,QAAQ;AAAA,UACR;AAAA,QACF;AACA,eAAO,IAAI,EAAE,IAAI;AAEjB,YAAI,GAAG,UAAU,SAAS,EAAE,GAAG;AAC7B,iBAAO,UAAU,OAAO,IAAI,EAAE,GAAG,EAAE;AAAA,QACrC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB;AAC/B,UAAI,SAAS;AAEb,aAAO,QAAQ,KAAK,GAAG,EAAE,QAAQ,SAAU,MAAM;AAC/C,YAAI,QAAQ,eAAe,MAAM,CAAC,GAC9B,IAAI,MAAM,CAAC,GACX,KAAK,MAAM,CAAC;AAEhB,YAAI,MAAM,GAAG,SAAS,sBAAsB,EAAE,MAAM,OAAO,SAAS,OAAO;AAE3E,YAAI,SAAS,MAAM,GAAG,SAAS;AAE/B,YAAI,iBAAiB,OAAO,kBAAkB,GAAG,MAAM;AAEvD,eAAO,IAAI,CAAC,EAAE,MAAM,MAAM,eAAe,CAAC;AAC1C,eAAO,IAAI,CAAC,EAAE,SAAS,SAAS,eAAe,CAAC;AAAA,MAClD,CAAC;AACD,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,kBAAkB,QAAQ;AACxC,UAAI,iBAAiB,CAAC,GAAG,CAAC;AAE1B,UAAI,QAAQ;AACV,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAI,OAAO,OAAO,CAAC,KAAK,UAAU;AAChC,gBAAI,OAAO,CAAC,EAAE,SAAS,GAAG,GAAG;AAC3B,6BAAe,CAAC,IAAI,SAAS,OAAO,CAAC,EAAE,QAAQ,KAAK,EAAE,IAAI,KAAK,eAAe,GAAG;AAAA,YACnF,OAAO;AACL,6BAAe,CAAC,IAAI,SAAS,OAAO,CAAC,CAAC;AAAA,YACxC;AAAA,UACF,OAAO;AACL,2BAAe,CAAC,IAAI,OAAO,CAAC;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,QAAQ;AAC/B,UAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEnF,UAAI,SAAS,SAAS,QAAQ,MAAM,KAAK;AAEzC,UAAI,WAAW,QAAQ,WAAW,QAAQ,WAAW;AAErD,UAAI,OAAO,WAAW,UAAU;AAE9B,YAAI,WAAW,OAAO;AACpB,mBAAS,KAAK;AAAA,QAChB,WAAW,WAAW,UAAU;AAC9B,mBAAS,KAAK,KAAK,eAAe,OAAO;AAAA,QAC3C,OAAO;AACL,mBAAS,SAAS,cAAc,MAAM;AAEtC,cAAI,CAAC,QAAQ;AACX;AAAA,UACF;AAAA,QACF;AAAA,MACF,WAAW,OAAO,WAAW,UAAU;AAErC,iBAAS,SAAS,MAAM;AAAA,MAC1B,WAAW,UAAU,OAAO,QAAS;AAAA,WAAO;AAC1C,gBAAQ,KAAK,iCAAiC;AAC9C;AAAA,MACF;AAGA,UAAI,OAAO,WAAW,UAAU;AAC9B,iBAAS,OAAO,sBAAsB,EAAE,MAAM,SAAS,KAAK,SAAS,OAAO;AAAA,MAC9E,OAAO;AACL,iBAAS,SAAS;AAAA,MACpB;AAEA,UAAI,kBAAkB,SAASC,mBAAkB;AAC/C,eAAO,SAAS,OAAO,WAAW,MAAM,SAAS,MAAM;AAAA,MACzD;AAEA,UAAI,UAAU;AACZ,YAAI,gBAAgB,GAAG;AACrB,mBAAS;AACT;AAAA,QACF,OAAO;AACL,cAAI,WAAW,SAASC,YAAW;AACjC,gBAAI,gBAAgB,GAAG;AACrB,qBAAO,oBAAoB,UAAUA,SAAQ;AAC7C,uBAAS;AAAA,YACX;AAAA,UACF;AAEA,iBAAO,iBAAiB,UAAU,QAAQ;AAAA,QAC5C;AAAA,MACF;AAEA,aAAO,SAAS;AAAA,QACd,KAAK;AAAA,QACL,UAAU,QAAQ,aAAa,IAAI,SAAS;AAAA,MAC9C,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,WAAK,YAAY;AACjB,WAAK,eAAe;AAAA,IACtB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACxB,WAAK,gBAAgBF,UAAS,SAAS,GAAG,WAAW,IAAI,EAAE,KAAK,IAAI;AAEpE,aAAO,oBAAoB,UAAU,KAAK,aAAa,KAAK;AAAA,IAC9D;AAAA,EACF,CAAC,CAAC;AAEF,SAAOA;AACT,EAAE,QAAQ;AAQV,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,mBAAmB,OAAO,UAAU;AAExC,SAAS,SAAS,KAAK;AACtB,MAAI,QAAQ,QAAQ,QAAQ,QAAW;AACtC,UAAM,IAAI,UAAU,uDAAuD;AAAA,EAC5E;AAEA,SAAO,OAAO,GAAG;AAClB;AAEA,SAAS,kBAAkB;AAC1B,MAAI;AACH,QAAI,CAAC,OAAO,QAAQ;AACnB,aAAO;AAAA,IACR;AAKA,QAAI,QAAQ,IAAI,OAAO,KAAK;AAC5B,UAAM,CAAC,IAAI;AACX,QAAI,OAAO,oBAAoB,KAAK,EAAE,CAAC,MAAM,KAAK;AACjD,aAAO;AAAA,IACR;AAGA,QAAI,QAAQ,CAAC;AACb,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,YAAM,MAAM,OAAO,aAAa,CAAC,CAAC,IAAI;AAAA,IACvC;AACA,QAAI,SAAS,OAAO,oBAAoB,KAAK,EAAE,IAAI,SAAU,GAAG;AAC/D,aAAO,MAAM,CAAC;AAAA,IACf,CAAC;AACD,QAAI,OAAO,KAAK,EAAE,MAAM,cAAc;AACrC,aAAO;AAAA,IACR;AAGA,QAAI,QAAQ,CAAC;AACb,2BAAuB,MAAM,EAAE,EAAE,QAAQ,SAAU,QAAQ;AAC1D,YAAM,MAAM,IAAI;AAAA,IACjB,CAAC;AACD,QAAI,OAAO,KAAK,OAAO,OAAO,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAC/C,wBAAwB;AACzB,aAAO;AAAA,IACR;AAEA,WAAO;AAAA,EACR,SAAS,KAAK;AAEb,WAAO;AAAA,EACR;AACD;AAEA,IAAI,eAAe,gBAAgB,IAAI,OAAO,SAAS,SAAU,QAAQ,QAAQ;AAChF,MAAI;AACJ,MAAI,KAAK,SAAS,MAAM;AACxB,MAAI;AAEJ,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,WAAO,OAAO,UAAU,CAAC,CAAC;AAE1B,aAAS,OAAO,MAAM;AACrB,UAAI,eAAe,KAAK,MAAM,GAAG,GAAG;AACnC,WAAG,GAAG,IAAI,KAAK,GAAG;AAAA,MACnB;AAAA,IACD;AAEA,QAAI,uBAAuB;AAC1B,gBAAU,sBAAsB,IAAI;AACpC,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,YAAI,iBAAiB,KAAK,MAAM,QAAQ,CAAC,CAAC,GAAG;AAC5C,aAAG,QAAQ,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;AAAA,QACjC;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AAEA,SAAS,IAAK;AAGd;AAEA,EAAE,YAAY;AAAA,EACZ,IAAI,SAAU,MAAM,UAAU,KAAK;AACjC,QAAI,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC;AAE7B,KAAC,EAAE,IAAI,MAAM,EAAE,IAAI,IAAI,CAAC,IAAI,KAAK;AAAA,MAC/B,IAAI;AAAA,MACJ;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,SAAU,MAAM,UAAU,KAAK;AACnC,QAAIL,QAAO;AACX,aAAS,WAAY;AACnB,MAAAA,MAAK,IAAI,MAAM,QAAQ;AACvB,eAAS,MAAM,KAAK,SAAS;AAAA,IAC/B;AACA,aAAS,IAAI;AACb,WAAO,KAAK,GAAG,MAAM,UAAU,GAAG;AAAA,EACpC;AAAA,EAEA,MAAM,SAAU,MAAM;AACpB,QAAI,OAAO,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC;AACrC,QAAI,WAAW,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,MAAM;AAC3D,QAAI,IAAI;AACR,QAAI,MAAM,OAAO;AAEjB,SAAK,GAAG,IAAI,KAAK,KAAK;AACpB,aAAO,CAAC,EAAE,GAAG,MAAM,OAAO,CAAC,EAAE,KAAK,IAAI;AAAA,IACxC;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,SAAU,MAAM,UAAU;AAC7B,QAAI,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC;AAC7B,QAAI,OAAO,EAAE,IAAI;AACjB,QAAI,aAAa,CAAC;AAElB,QAAI,QAAQ,UAAU;AACpB,eAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AAC/C,YAAI,KAAK,CAAC,EAAE,OAAO,YAAY,KAAK,CAAC,EAAE,GAAG,MAAM;AAC9C,qBAAW,KAAK,KAAK,CAAC,CAAC;AAAA,MAC3B;AAAA,IACF;AAMA,IAAC,WAAW,SACR,EAAE,IAAI,IAAI,aACV,OAAO,EAAE,IAAI;AAEjB,WAAO;AAAA,EACT;AACF;AAEA,IAAI,cAAc;AAElB,IAAI,WAAW,qBAAqB,SAAU,QAAQ,SAAS;AAE/D,GAAC,WAAW;AACV,QAAI;AAEJ,WAAQ,YAAY,OAAO,UAAU;AAErC,SAAK,WAAY,WAAW;AAC1B,eAASQ,UAAS,WAAW,aAAa,WAAW,OAAO;AAC1D,aAAK,YAAY,aAAa,OAAO,KAAK,IAAI,SAAS,IAAI;AAC3D,aAAK,cAAc,eAAe,OAAO,IAAI,KAAK,IAAI,WAAW,IAAI;AACrE,aAAK,YAAY,aAAa,OAAO,IAAI,KAAK,IAAI,SAAS,IAAI;AAC/D,aAAK,QAAQ,SAAS,OAAO,QAAQ;AACrC,aAAK,gBAAgB,WAAW;AAC9B,cAAI,GAAG,KAAK;AACZ,oBAAU,CAAC;AACX,eAAK,IAAI,GAAG,MAAM,KAAK,YAAY,GAAG,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK;AAC1F,oBAAQ,KAAK,IAAI;AAAA,UACnB;AACA,iBAAO;AAAA,QACT,GAAG,KAAK,IAAI;AACZ,aAAK,kBAAkB,WAAW;AAChC,cAAI,GAAG,KAAK;AACZ,oBAAU,CAAC;AACX,eAAK,IAAI,GAAG,MAAM,KAAK,YAAY,GAAG,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK;AAC1F,oBAAQ,KAAK,IAAI;AAAA,UACnB;AACA,iBAAO;AAAA,QACT,GAAG,KAAK,IAAI;AACZ,aAAK,mBAAmB,WAAW;AACjC,cAAI,GAAG,KAAK;AACZ,oBAAU,CAAC;AACX,eAAK,IAAI,GAAG,MAAM,KAAK,YAAY,GAAG,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK;AAC1F,oBAAQ,KAAK,IAAI;AAAA,UACnB;AACA,iBAAO;AAAA,QACT,GAAG,KAAK,IAAI;AAAA,MACd;AAEA,MAAAA,UAAS,UAAU,QAAQ,SAAS,GAAG;AACrC,YAAI;AACJ,YAAI,EAAE,iBAAiB;AACvB,YAAI,EAAE,cAAc,MAAM;AACxB,sBAAY,EAAE;AAAA,QAChB,WAAW,EAAE,UAAU,MAAM;AAC3B,sBAAY,EAAE,SAAS;AAAA,QACzB,WAAY,EAAE,UAAU,QAAS,EAAE,WAAW,GAAG;AAC/C,sBAAY,EAAE,SAAS;AAAA,QACzB;AACA,aAAK,gBAAgB,KAAK,KAAK,IAAI,CAAC;AACpC,aAAK,gBAAgB,MAAM;AAC3B,YAAI,YAAY,GAAG;AACjB,eAAK,aAAa,KAAK,SAAS;AAChC,eAAK,aAAa,MAAM;AACxB,iBAAO,KAAK,UAAU,CAAC;AAAA,QACzB,OAAO;AACL,eAAK,eAAe,KAAK,SAAS;AAClC,eAAK,eAAe,MAAM;AAC1B,iBAAO,KAAK,UAAU,EAAE;AAAA,QAC1B;AAAA,MACF;AAEA,MAAAA,UAAS,UAAU,YAAY,SAAS,WAAW;AACjD,YAAI,YAAY,eAAe,eAAe,YAAY,QAAQ,YAAY;AAC9E,qBAAa,cAAc,KAAK,KAAK,iBAAiB,KAAK;AAC3D,YAAI,WAAW,CAAC,MAAM,MAAM;AAC1B,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,gBAAiB,KAAK,YAAY,IAAK,CAAC,IAAI,KAAK,QAAQ,KAAK,IAAI,KAAK,WAAW,CAAC,MAAM,WAAY,KAAK,YAAY,IAAK,CAAC,GAAG;AACtI,iBAAO;AAAA,QACT;AACA,wBAAgB,WAAW,MAAM,GAAG,KAAK,SAAS;AAClD,wBAAgB,WAAW,MAAM,KAAK,WAAW,KAAK,YAAY,CAAC;AACnE,iBAAS,cAAc,OAAO,SAAS,GAAG,GAAG;AAC3C,iBAAO,IAAI;AAAA,QACb,CAAC;AACD,iBAAS,cAAc,OAAO,SAAS,GAAG,GAAG;AAC3C,iBAAO,IAAI;AAAA,QACb,CAAC;AACD,qBAAa,SAAS,cAAc;AACpC,qBAAa,SAAS,cAAc;AACpC,YAAI,KAAK,IAAI,UAAU,IAAI,KAAK,IAAI,aAAa,KAAK,SAAS,KAAM,KAAK,cAAc,KAAK,IAAI,UAAU,GAAI;AAC7G,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,MAAAA,UAAS,UAAU,mBAAmB,WAAW;AAC/C,eAAO,KAAK;AAAA,MACd;AAEA,MAAAA,UAAS,UAAU,qBAAqB,WAAW;AACjD,eAAO,KAAK;AAAA,MACd;AAEA,aAAOA;AAAA,IAET,EAAG;AAAA,EAEL,GAAG,KAAK,cAAc;AACtB,CAAC;AAED,IAAI,UAAW,SAAS,aAAa;AACjC,SAAO;AAAA,IACH,eAAe,aAAa;AAAA,IAC5B,oBAAoB,kBAAkB;AAAA,IACtC,UAAW,kBAAkB,UAAW,OAAO,cAAc,OAAO,iBAAiB,oBAAoB;AAAA,IACzG,aAAa,UAAU,oBAAoB,UAAU,mBAAmB;AAAA,IACxE,YAAY,CAAC,CAAC,OAAO,UAAU;AAAA,IAC/B,YAAY,eAAe;AAAA,IAC3B,WAAW,UAAU,UAAU,QAAQ,SAAS,IAAI;AAAA,EACxD;AACJ,EAAG;AAEH,IAAI,WAAW,OAAO,UAAU;AAAhC,IACI,mBAAmB,OAAO,UAAU;AAExC,IAAI,oBAAoB,SAAS,QAAQ;AACrC,MAAG,CAAC,OAAQ,QAAO,QAAQ,KAAK,yCAAyC;AAEzE,MAAI,YAAY,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAEvD,MAAI,UAAU,WAAW,GAAG;AAExB,aAAS,UAAU,QAAQ;AACvB,UAAG,iBAAiB,KAAK,QAAQ,MAAM,GAAG;AACtC,YAAG,OAAO,OAAO,MAAM,KAAK,cAAc,SAAS,KAAK,OAAO,MAAM,CAAC,KAAK,qBAAqB;AAC5F,oBAAU,KAAK,MAAM;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,WAAQ,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACtC,QAAI,IAAI,UAAU,CAAC;AACnB,WAAO,CAAC,IAAI,KAAK,OAAO,CAAC,GAAG,MAAM;AAAA,EACtC;AACJ;AAOA,SAAS,KAAK,MAAM,SAAS;AAC3B,SAAO,WAAW;AAChB,WAAO,KAAK,MAAM,SAAS,SAAS;AAAA,EACtC;AACF;AAEA,IAAI,WAAW,SAAS;AAIxB,IAAI,SAAS;AAEb,IAAI,MAAM;AAEV,IAAI,WAAW;AAAA,EACX,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AACX;AAEA,SAAS,cAAc,SAAS;AAC5B,oBAAkB,MAAM,YAAY,iBAAiB,iBAAiB,gBAAgB,YAAY;AAElG,OAAK,KAAK;AACV,MAAI,WAAW,QAAQ,IAAI;AACvB,SAAK,KAAK,QAAQ;AAClB,WAAO,QAAQ;AAAA,EACnB;AACA,OAAK,UAAU,aAAa;AAAA,IACxB,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,SAAS;AAAA,IACT,cAAc;AAAA,IACd,qBAAqB;AAAA,IACrB,cAAc;AAAA,IACd,aAAa;AAAA,IACb,UAAU;AAAA,EACd,GAAG,OAAO;AAEV,MAAI,KAAK,QAAQ,aAAc,MAAK,YAAY,IAAI,SAAS;AAE7D,OAAK,WAAW,IAAI,YAAY;AAChC,OAAK,SAAS;AAAA,IACV,GAAG;AAAA,IACH,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACA,OAAK,cAAc;AACnB,OAAK,cAAc;AACnB,OAAK,kBAAkB;AAEvB,MAAI,KAAK,QAAQ,YAAY,QAAW;AACpC,SAAK,kBAAkB,EAAC,SAAS,KAAK,QAAQ,QAAO;AAAA,EACzD;AACJ;AAEA,cAAc,UAAU,UAAU,SAAS,GAAG;AAC1C,MAAI,MAAM,KAAK;AACf,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AAEd,OAAK,SAAS,KAAK,QAAQ;AAAA,IACtB,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,IACP,QAAQ,IAAI;AAAA,IACZ,QAAQ,IAAI;AAAA,IACZ,eAAe;AAAA,EACpB,CAAC;AACJ;AAEA,cAAc,UAAU,WAAW,SAAS,GAAG;AAC3C,MAAI,UAAU,KAAK;AACnB,MAAI,KAAK,aAAa,KAAK,UAAU,MAAM,CAAC,MAAM,MAAO;AACzD,MAAI,MAAM,KAAK;AAGf,MAAI,SAAS,EAAE,eAAe,EAAE,SAAS;AACzC,MAAI,SAAS,EAAE,eAAe,EAAE,SAAS;AAIzC,MAAG,QAAQ,aAAa,EAAE,aAAa,GAAG;AACtC,QAAI,UAAU,QAAQ;AACtB,QAAI,UAAU,QAAQ;AAAA,EAC1B;AAEA,MAAI,UAAU,QAAQ;AACtB,MAAI,UAAU,QAAQ;AAEtB,OAAK,QAAQ,CAAC;AAClB;AAEA,cAAc,UAAU,gBAAgB,SAAS,GAAG;AAChD,MAAI,KAAK,QAAQ,gBAAgB,KAAK,UAAU,MAAM,CAAC,MAAM,MAAO;AAEpE,MAAI,MAAM,KAAK;AAGf,MAAI,SAAU,EAAE,cAAe,EAAE,cAAc;AAC/C,MAAI,SAAU,EAAE,cAAe,EAAE,cAAc,EAAE;AAEjD,OAAK,QAAQ,CAAC;AAClB;AAEA,cAAc,UAAU,gBAAgB,SAAS,GAAG;AAChD,MAAI,IAAK,EAAE,gBAAiB,EAAE,cAAc,CAAC,IAAI;AACjD,OAAK,cAAc,EAAE;AACrB,OAAK,cAAc,EAAE;AACzB;AAEA,cAAc,UAAU,eAAe,SAAS,GAAG;AAC/C,MAAI,UAAU,KAAK;AACnB,MAAG,QAAQ,gBACJ,CAAC,EAAE,OAAO,UAAU,SAAS,QAAQ,mBAAmB,GAAG;AAC9D,MAAE,eAAe;AAAA,EACrB;AAEA,MAAI,MAAM,KAAK;AAEf,MAAI,IAAK,EAAE,gBAAiB,EAAE,cAAc,CAAC,IAAI;AAEjD,MAAI,UAAU,EAAE,QAAQ,KAAK,eAAe,QAAQ;AACpD,MAAI,UAAU,EAAE,QAAQ,KAAK,eAAe,QAAQ;AAEpD,OAAK,cAAc,EAAE;AACrB,OAAK,cAAc,EAAE;AAErB,OAAK,QAAQ,CAAC;AAClB;AAEA,cAAc,UAAU,aAAa,SAAS,GAAG;AAC7C,MAAI,MAAM,KAAK;AACf,MAAI,SAAS,IAAI,SAAS;AAC1B,MAAI,eAAe,OAAO,cAAc;AAExC,UAAO,EAAE,SAAS;AAAA,IACd,KAAK,SAAS;AAAA,IACd,KAAK,SAAS;AACV,UAAI,SAAS,KAAK,QAAQ;AAC1B;AAAA,IAEJ,KAAK,SAAS;AAAA,IACd,KAAK,SAAS;AACV,UAAI,SAAS,CAAE,KAAK,QAAQ;AAC5B;AAAA,IACJ,KAAM,EAAE;AACJ,UAAI,SAAS;AACb;AAAA,IACJ,KAAK,SAAS;AACV,UAAI,SAAS,CAAE;AACf;AAAA,IACJ;AACI;AAAA,EACR;AAEA,OAAK,QAAQ,CAAC;AAClB;AAEA,cAAc,UAAU,QAAQ,WAAW;AACvC,MAAG,QAAQ,cAAe,MAAK,GAAG,iBAAiB,SAAS,KAAK,UAAU,KAAK,eAAe;AAC/F,MAAG,QAAQ,mBAAoB,MAAK,GAAG,iBAAiB,cAAc,KAAK,eAAe,KAAK,eAAe;AAE9G,MAAG,QAAQ,YAAY,KAAK,QAAQ,UAAU;AAC1C,SAAK,GAAG,iBAAiB,cAAc,KAAK,eAAe,KAAK,eAAe;AAC/E,SAAK,GAAG,iBAAiB,aAAa,KAAK,cAAc,KAAK,eAAe;AAAA,EACjF;AAEA,MAAG,QAAQ,cAAc,QAAQ,aAAa;AAC1C,SAAK,kBAAkB,SAAS,KAAK,MAAM;AAC3C,aAAS,KAAK,MAAM,gBAAgB;AACpC,SAAK,GAAG,iBAAiB,iBAAiB,KAAK,eAAe,IAAI;AAClE,SAAK,GAAG,iBAAiB,iBAAiB,KAAK,cAAc,IAAI;AAAA,EACrE;AAEA,MAAG,QAAQ,cAAc,KAAK,QAAQ,YAAa,UAAS,iBAAiB,WAAW,KAAK,UAAU;AAC3G;AAEA,cAAc,UAAU,UAAU,WAAW;AACzC,MAAG,QAAQ,cAAe,MAAK,GAAG,oBAAoB,SAAS,KAAK,QAAQ;AAC5E,MAAG,QAAQ,mBAAoB,MAAK,GAAG,oBAAoB,cAAc,KAAK,aAAa;AAE3F,MAAG,QAAQ,UAAU;AACjB,SAAK,GAAG,oBAAoB,cAAc,KAAK,aAAa;AAC5D,SAAK,GAAG,oBAAoB,aAAa,KAAK,YAAY;AAAA,EAC9D;AAEA,MAAG,QAAQ,cAAc,QAAQ,aAAa;AAC1C,aAAS,KAAK,MAAM,gBAAgB,KAAK;AACzC,SAAK,GAAG,oBAAoB,iBAAiB,KAAK,eAAe,IAAI;AACrE,SAAK,GAAG,oBAAoB,iBAAiB,KAAK,cAAc,IAAI;AAAA,EACxE;AAEA,MAAG,QAAQ,cAAc,KAAK,QAAQ,YAAa,UAAS,oBAAoB,WAAW,KAAK,UAAU;AAC9G;AAEA,cAAc,UAAU,KAAK,SAAS,IAAI,KAAK;AAC7C,OAAK,SAAS,GAAG,QAAQ,IAAI,GAAG;AAEhC,MAAI,SAAS,KAAK,SAAS;AAC3B,MAAI,UAAU,OAAO,MAAM,KAAK,OAAO,MAAM,EAAE,WAAW,EAAG,MAAK,MAAM;AAC1E;AAEA,cAAc,UAAU,MAAM,SAAS,IAAI,KAAK;AAC9C,OAAK,SAAS,IAAI,QAAQ,IAAI,GAAG;AAEjC,MAAI,SAAS,KAAK,SAAS;AAC3B,MAAI,CAAC,OAAO,MAAM,KAAK,OAAO,MAAM,EAAE,UAAU,EAAG,MAAK,QAAQ;AAClE;AAEA,cAAc,UAAU,QAAQ,WAAW;AACvC,MAAI,MAAM,KAAK;AACf,MAAI,IAAI;AACR,MAAI,IAAI;AACZ;AAEA,cAAc,UAAU,UAAU,WAAW;AACzC,OAAK,SAAS,IAAI;AAClB,OAAK,QAAQ;AACjB;AAEA,SAAS,KAAK,OAAO,KAAK,KAAK;AAC7B,UAAQ,IAAI,OAAO,QAAQ,MAAM;AACnC;AAEA,SAAS,aAAa,IAAI;AACxB,MAAI,YAAY,CAAC;AACjB,MAAI,CAAC,OAAO,iBAAkB;AAC9B,MAAI,QAAQ,iBAAiB,EAAE;AAC/B,MAAI,YAAY,MAAM,aAAa,MAAM,mBAAmB,MAAM;AAClE,MAAI,MAAM,UAAU,MAAM,oBAAoB;AAE9C,MAAI,KAAK;AACP,cAAU,IAAI,MAAM,WAAW,IAAI,CAAC,EAAE,MAAM,IAAI,EAAE,EAAE,CAAC,IAAI;AACzD,cAAU,IAAI,MAAM,WAAW,IAAI,CAAC,EAAE,MAAM,IAAI,EAAE,EAAE,CAAC,IAAI;AAAA,EAC3D,OAAO;AACL,UAAM,UAAU,MAAM,kBAAkB;AACxC,cAAU,IAAI,MAAM,WAAW,IAAI,CAAC,EAAE,MAAM,IAAI,EAAE,CAAC,CAAC,IAAI;AACxD,cAAU,IAAI,MAAM,WAAW,IAAI,CAAC,EAAE,MAAM,IAAI,EAAE,CAAC,CAAC,IAAI;AAAA,EAC1D;AAEA,SAAO;AACT;AAOA,SAAS,WAAW,MAAM;AAExB,MAAI,UAAU,CAAC;AAEf,SAAO,QAAQ,SAAS,UAAU,OAAO,KAAK,YAAY;AACxD,YAAQ,KAAK,IAAI;AAAA,EACnB;AAGA,SAAO;AACT;AASA,IAAI,oBAAoB;AACxB,IAAI,mBAAmB;AACvB,IAAI,wBAAwB;AAC5B,IAAI,6BAA6B;AAEjC,IAAI,mBAAmB;AACvB,IAAI,kBAAkB,KAAO,mBAAmB;AAEhD,IAAI,wBAAwB,OAAO,iBAAiB;AAEpD,SAAS,EAAG,KAAK,KAAK;AAAE,SAAO,IAAM,IAAM,MAAM,IAAM;AAAK;AAC5D,SAAS,EAAG,KAAK,KAAK;AAAE,SAAO,IAAM,MAAM,IAAM;AAAK;AACtD,SAAS,EAAG,KAAU;AAAE,SAAO,IAAM;AAAK;AAG1C,SAAS,WAAY,IAAI,KAAK,KAAK;AAAE,WAAS,EAAE,KAAK,GAAG,IAAI,KAAK,EAAE,KAAK,GAAG,KAAK,KAAK,EAAE,GAAG,KAAK;AAAI;AAGnG,SAAS,SAAU,IAAI,KAAK,KAAK;AAAE,SAAO,IAAM,EAAE,KAAK,GAAG,IAAI,KAAK,KAAK,IAAM,EAAE,KAAK,GAAG,IAAI,KAAK,EAAE,GAAG;AAAG;AAEzG,SAAS,gBAAiB,IAAI,IAAI,IAAI,KAAK,KAAK;AAC9C,MAAI,UAAU,UAAU,IAAI;AAC5B,KAAG;AACD,eAAW,MAAM,KAAK,MAAM;AAC5B,eAAW,WAAW,UAAU,KAAK,GAAG,IAAI;AAC5C,QAAI,WAAW,GAAK;AAClB,WAAK;AAAA,IACP,OAAO;AACL,WAAK;AAAA,IACP;AAAA,EACF,SAAS,KAAK,IAAI,QAAQ,IAAI,yBAAyB,EAAE,IAAI;AAC7D,SAAO;AACT;AAEA,SAAS,qBAAsB,IAAI,SAAS,KAAK,KAAK;AACrD,WAAS,IAAI,GAAG,IAAI,mBAAmB,EAAE,GAAG;AAC1C,QAAI,eAAe,SAAS,SAAS,KAAK,GAAG;AAC7C,QAAI,iBAAiB,GAAK;AACxB,aAAO;AAAA,IACT;AACA,QAAI,WAAW,WAAW,SAAS,KAAK,GAAG,IAAI;AAC/C,eAAW,WAAW;AAAA,EACxB;AACA,SAAO;AACR;AAEA,SAAS,aAAc,GAAG;AACxB,SAAO;AACT;AAEA,IAAI,QAAQ,SAAS,OAAQ,KAAK,KAAK,KAAK,KAAK;AAC/C,MAAI,EAAE,KAAK,OAAO,OAAO,KAAK,KAAK,OAAO,OAAO,IAAI;AACnD,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC3D;AAEA,MAAI,QAAQ,OAAO,QAAQ,KAAK;AAC9B,WAAO;AAAA,EACT;AAGA,MAAI,eAAe,wBAAwB,IAAI,aAAa,gBAAgB,IAAI,IAAI,MAAM,gBAAgB;AAC1G,WAAS,IAAI,GAAG,IAAI,kBAAkB,EAAE,GAAG;AACzC,iBAAa,CAAC,IAAI,WAAW,IAAI,iBAAiB,KAAK,GAAG;AAAA,EAC5D;AAEA,WAAS,SAAU,IAAI;AACrB,QAAI,gBAAgB;AACpB,QAAI,gBAAgB;AACpB,QAAI,aAAa,mBAAmB;AAEpC,WAAO,kBAAkB,cAAc,aAAa,aAAa,KAAK,IAAI,EAAE,eAAe;AACzF,uBAAiB;AAAA,IACnB;AACA,MAAE;AAGF,QAAI,QAAQ,KAAK,aAAa,aAAa,MAAM,aAAa,gBAAgB,CAAC,IAAI,aAAa,aAAa;AAC7G,QAAI,YAAY,gBAAgB,OAAO;AAEvC,QAAI,eAAe,SAAS,WAAW,KAAK,GAAG;AAC/C,QAAI,gBAAgB,kBAAkB;AACpC,aAAO,qBAAqB,IAAI,WAAW,KAAK,GAAG;AAAA,IACrD,WAAW,iBAAiB,GAAK;AAC/B,aAAO;AAAA,IACT,OAAO;AACL,aAAO,gBAAgB,IAAI,eAAe,gBAAgB,iBAAiB,KAAK,GAAG;AAAA,IACrF;AAAA,EACF;AAEA,SAAO,SAAS,aAAc,GAAG;AAE/B,QAAI,MAAM,GAAG;AACX,aAAO;AAAA,IACT;AACA,QAAI,MAAM,GAAG;AACX,aAAO;AAAA,IACT;AACA,WAAO,WAAW,SAAS,CAAC,GAAG,KAAK,GAAG;AAAA,EACzC;AACF;AAEA,IAAI,aAAa;AAAA,EACf,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,MAAM;AAAA,EACN,KAAK;AACP;AAEA,IAAI,aAA0B,SAAU,OAAO;AAC7C,YAAUH,WAAU,KAAK;AAEzB,MAAI,SAAS,aAAaA,SAAQ;AAElC,WAASA,YAAW;AAClB,QAAI;AAEJ,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEnF,oBAAgB,MAAMA,SAAQ;AAE9B,QAAI,QAAQ,mBAAmB;AAC7B,cAAQ,oBAAoB;AAAA,IAC9B;AAEA,WAAO,SAAS,GAAG,CAAC;AACpB,YAAQ,OAAO,KAAK,MAAM,OAAO;AACjC,QAAI,MAAM,QAAS,OAAM,OAAO,MAAM,UAAU;AAChD,UAAM,cAAc;AACpB,UAAM,sBAAsB;AAC5B,UAAM,YAAY;AAClB,UAAM,mBAAmB;AACzB,UAAM,mBAAmB,CAAC;AAC1B,UAAM,OAAO;AACb,UAAM,qBAAqB,QAAQ;AACnC,UAAM,WAAW,MAAM,SAAS,KAAK,uBAAuB,KAAK,CAAC;AAClE,WAAO,iBAAiB,WAAW,MAAM,UAAU,KAAK;AACxD,WAAO;AAAA,EACT;AAEA,eAAaA,WAAU,CAAC;AAAA,IACtB,KAAK;AAAA,IACL,OAAO,SAAS,OAAO;AACrB,UAAI,SAAS;AAEb,WAAK,KAAK,UAAU,IAAI,KAAK,WAAW;AACxC,WAAK,KAAK,aAAa,QAAQ,OAAO,KAAK,MAAM,YAAY,GAAG,KAAK,SAAS;AAC9E,WAAK,WAAW,eAAe;AAAA,QAC7B,OAAO;AAAA,UACL,GAAG,KAAK,aAAa;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB;AAAA,QACA,QAAQ;AAAA,UACN,GAAG,KAAK,aAAa;AAAA,UACrB,GAAG,KAAK,aAAa;AAAA,QACvB;AAAA,MACF,GAAG,KAAK,QAAQ;AAChB,WAAK,KAAK,IAAI,IAAI;AAAA,QAChB,IAAI,KAAK,qBAAqB,WAAW,KAAK;AAAA,QAC9C,iBAAiB,UAAU,SAAS,QAAQ,KAAK,IAAI,KAAK,IAAI;AAAA,QAC9D,mBAAmB,KAAK;AAAA,QACxB,iBAAiB,KAAK;AAAA,QACtB,aAAa;AAAA,QACb,SAAS;AAAA,MACX,CAAC;AACD,WAAK,GAAG,GAAG,SAAU,GAAG;AACtB,YAAI,OAAO,MAAM;AACf;AAAA,QACF;AAEA,YAAI,CAAC,OAAO,qBAAqB;AAC/B,gCAAsB,WAAY;AAChC,mBAAO,YAAY,CAAC;AAEpB,gBAAI,CAAC,OAAO,YAAa,QAAO,eAAe;AAAA,UACjD,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,WAAK,eAAe;AACpB,WAAK,cAAc;AACnB,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,WAAK,YAAY,IAAI;AACrB,WAAK,kBAAkB,MAAM,IAAI;AAEjC,WAAK,gBAAgBA,UAAS,SAAS,GAAG,QAAQ,IAAI,EAAE,KAAK,IAAI;AAAA,IACnE;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB;AAC/B,WAAK,SAAS,MAAM,IAAI,KAAK,GAAG,eAAe,KAAK;AAEpD,UAAI,KAAK,cAAc,cAAc;AACnC,YAAI,aAAa;AACjB,YAAI,QAAQ,KAAK,GAAG;AAEpB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,wBAAc,MAAM,CAAC,EAAE;AAAA,QACzB;AAEA,aAAK,SAAS,MAAM,IAAI,aAAa,KAAK;AAAA,MAC5C;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB;AAC/B,WAAK,gBAAgB,KAAK,IAAI;AAE9B,WAAK,cAAc;AACnB,WAAK,YAAY;AACjB,WAAK,KAAK,UAAU,IAAI,KAAK,cAAc;AAAA,IAC7C;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB;AAC9B,2BAAqB,KAAK,cAAc;AAGxC,WAAK,gBAAgB;AAErB,UAAI,KAAK,aAAa;AACpB,6BAAqB,KAAK,WAAW;AACrC,aAAK,cAAc;AAAA,MACrB;AAEA,WAAK,cAAc;AACnB,WAAK,SAAS,OAAO,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO,CAAC;AAC1D,WAAK,KAAK,UAAU,OAAO,KAAK,cAAc;AAAA,IAChD;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,GAAG;AAC1B,UAAI,SAAS;AAEb,UAAI,KAAK,MAAM;AAGb,YAAI,EAAE,WAAW,WAAW,KAAK;AAC/B,gCAAsB,WAAY;AAEhC,mBAAO,KAAK,YAAY;AACxB,qBAAS,KAAK,YAAY;AAC1B,mBAAO,KAAK,aAAa;AACzB,qBAAS,KAAK,aAAa;AAAA,UAC7B,CAAC;AAAA,QACH;AAEA;AAAA,MACF;AAEA,cAAQ,EAAE,SAAS;AAAA,QACjB,KAAK,WAAW;AAGd,gCAAsB,WAAY;AAEhC,mBAAO,KAAK,YAAY;AACxB,qBAAS,KAAK,YAAY;AAC1B,mBAAO,KAAK,aAAa;AACzB,qBAAS,KAAK,aAAa;AAE3B,mBAAO,SAAS,SAAS,eAAe;AAAA,cACtC,QAAQ,CAAC,OAAO,cAAc;AAAA,YAChC,CAAC;AAAA,UACH,CAAC;AACD;AAAA,QAEF,KAAK,WAAW;AACd,cAAI,KAAK,+BAA+B,GAAG;AACzC,iBAAK,SAAS,MAAM,KAAK,aAAa,KAAK;AAAA,UAC7C;AAEA;AAAA,QAEF,KAAK,WAAW;AACd,cAAI,KAAK,+BAA+B,GAAG;AACzC,iBAAK,SAAS,MAAM,KAAK,aAAa,KAAK;AAAA,UAC7C;AAEA;AAAA,QAEF,KAAK,WAAW;AACd,eAAK,SAAS,MAAM,KAAK,aAAa,KAAK,OAAO;AAClD;AAAA,QAEF,KAAK,WAAW;AACd,eAAK,SAAS,MAAM,KAAK,aAAa,KAAK,OAAO;AAClD;AAAA,QAEF,KAAK,WAAW;AACd,eAAK,SAAS,MAAM,KAAK,aAAa,KAAK,KAAK,SAAS,MAAM,KAAK,aAAa;AACjF;AAAA,QAEF,KAAK,WAAW;AACd,eAAK,SAAS,MAAM,KAAK,aAAa,KAAK,KAAK,SAAS,MAAM,KAAK,aAAa;AACjF;AAAA,QAEF,KAAK,WAAW;AACd,cAAI,KAAK,+BAA+B,GAAG;AACzC,gBAAI,EAAE,UAAU;AACd,mBAAK,SAAS,MAAM,KAAK,aAAa,KAAK,OAAO;AAAA,YACpD,OAAO;AACL,mBAAK,SAAS,MAAM,KAAK,aAAa,KAAK,OAAO;AAAA,YACpD;AAAA,UACF;AAEA;AAAA,QAEF;AACE;AAAA,MACJ;AAEA,UAAI,KAAK,SAAS,MAAM,KAAK,aAAa,IAAI,EAAG,MAAK,SAAS,MAAM,KAAK,aAAa,IAAI;AAC3F,UAAI,KAAK,SAAS,MAAM,KAAK,aAAa,IAAI,KAAK,SAAS,MAAM,KAAK,aAAa,EAAG,MAAK,SAAS,MAAM,KAAK,aAAa,IAAI,KAAK,SAAS,MAAM,KAAK,aAAa;AACvK,WAAK,cAAc;AAEnB,WAAK,cAAc;AACnB,WAAK,YAAY;AACjB,WAAK,KAAK,UAAU,IAAI,KAAK,cAAc;AAAA,IAC7C;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iCAAiC;AAC/C,aAAO,EAAE,SAAS,yBAAyB,qBAAqB,EAAE,SAAS,yBAAyB,wBAAwB,EAAE,SAAS,yBAAyB,sBAAsB,EAAE,SAAS,yBAAyB;AAAA,IAC5N;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,UAAI,SAAS;AAEb,UAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEjF,UAAI,UAAU,KAAK,eAAe,KAAK,qBAAqB;AAC1D,YAAI,CAAC,KAAK,kBAAkB;AAC1B,eAAK,iBAAiB,sBAAsB,WAAY;AACtD,mBAAO,OAAO,YAAY;AAAA,UAC5B,CAAC;AACD,eAAK,mBAAmB;AAAA,QAC1B;AAEA,aAAK,aAAa;AAClB,YAAI,WAAW,KAAK,IAAI,KAAK,SAAS,MAAM,KAAK,aAAa,IAAI,KAAK,SAAS,OAAO,KAAK,aAAa,CAAC;AAC1G,YAAI,iBAAiB,KAAK,IAAI,IAAI,KAAK;AAEvC,YAAI,CAAC,KAAK,mBAAmB,iBAAiB,QAAQ,WAAW,OAAO,KAAK,SAAS,MAAM,KAAK,aAAa,KAAK,KAAK,WAAW,OAAO,KAAK,SAAS,MAAM,KAAK,aAAa,KAAK,IAAI;AACvL,eAAK,cAAc;AAAA,QACrB;AAEA,eAAO,QAAQ,KAAK,QAAQ,EAAE,QAAQ,SAAU,MAAM;AACpD,cAAI,QAAQ,eAAe,MAAM,CAAC,GAC9B,IAAI,MAAM,CAAC,GACX,UAAU,MAAM,CAAC;AAErB,cAAI,QAAQ,cAAc,OAAO,SAAS,OAAO,OAAO,aAAa,IAAI,QAAQ,OAAO,OAAO,aAAa,KAAK,OAAO,SAAS,OAAO,OAAO,aAAa,IAAI,QAAQ,MAAM,OAAO,aAAa,GAAG;AACnM,gBAAI,OAAO,cAAc,cAAc;AACrC,qBAAO,UAAU,QAAQ,IAAI,CAAC,OAAO,SAAS,OAAO,OAAO,aAAa,GAAG,CAAC;AAAA,YAC/E,OAAO;AACL,qBAAO,UAAU,QAAQ,IAAI,GAAG,CAAC,OAAO,SAAS,OAAO,OAAO,aAAa,CAAC;AAAA,YAC/E;AAEA,gBAAI,CAAC,QAAQ,QAAQ;AACnB,sBAAQ,SAAS;AACjB,sBAAQ,GAAG,MAAM,UAAU;AAC3B,sBAAQ,GAAG,MAAM,gBAAgB;AACjC,sBAAQ,GAAG,aAAa,QAAQ,OAAO,OAAO,MAAM,iBAAiB,GAAG,EAAE;AAAA,YAC5E;AAAA,UACF,OAAO;AACL,gBAAI,QAAQ,UAAU,QAAQ;AAC5B,sBAAQ,SAAS;AACjB,sBAAQ,GAAG,MAAM,UAAU;AAC3B,sBAAQ,GAAG,MAAM,gBAAgB;AACjC,sBAAQ,GAAG,gBAAgB,QAAQ,OAAO,OAAO,MAAM,iBAAiB,CAAC;AAAA,YAC3E;AAEA,mBAAO,UAAU,QAAQ,IAAI,GAAG,CAAC;AAAA,UACnC;AAAA,QACF,CAAC;AAED,YAAI,KAAK,cAAc;AACrB,eAAK,aAAa;AAAA,QACpB;AAEA,YAAI,KAAK,UAAU;AACjB,eAAK,SAAS;AACd,eAAK,UAAU,KAAK,IAAI;AAAA,QAC1B;AAEA,aAAK,eAAe;AACpB,aAAK,kBAAkB;AAEvB,YAAI,KAAK,cAAc;AACrB,cAAI,uBAAuB,KAAK,SAAS,OAAO,KAAK,aAAa,IAAI,KAAK,SAAS,MAAM,KAAK,aAAa,IAAI,KAAK,eAAe,KAAK,aAAa;AAEtJ,cAAI,KAAK,cAAc,cAAc;AACnC,iBAAK,UAAU,KAAK,gBAAgB,sBAAsB,CAAC;AAAA,UAC7D,OAAO;AACL,iBAAK,UAAU,KAAK,gBAAgB,GAAG,oBAAoB;AAAA,UAC7D;AAAA,QACF;AAEA,aAAK,gBAAgBA,UAAS,SAAS,GAAG,eAAe,IAAI,EAAE,KAAK,IAAI;AAExE,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,WAAK,eAAe,OAAO;AAC3B,WAAK,cAAc,OAAO;AAC1B,WAAK,aAAa;AAClB,WAAK,eAAe;AAAA,QAClB,GAAG,KAAK,cAAc;AAAA,QACtB,GAAG,KAAK,eAAe;AAAA,MACzB;AACA,WAAK,OAAO;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,GAAG;AAC7B,UAAI;AACJ,UAAI,mBAAmB,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,OAAO,EAAE,mBAAmB,KAAK,KAAK,OAAO,EAAE,mBAAmB,KAAK;AAE9H,UAAI,qBAAqB,QAAQ;AAC/B,gBAAQ,EAAE,SAAS,EAAE;AAAA,MACvB,WAAW,qBAAqB,YAAY;AAC1C,gBAAQ,EAAE;AAAA,MACZ,WAAW,qBAAqB,cAAc;AAC5C,gBAAQ,EAAE;AAAA,MACZ,OAAO;AACL,gBAAQ,EAAE;AAAA,MACZ;AAEA,WAAK,SAAS,MAAM,KAAK,aAAa,KAAK,QAAQ,KAAK;AACxD,UAAI,KAAK,SAAS,MAAM,KAAK,aAAa,IAAI,EAAG,MAAK,SAAS,MAAM,KAAK,aAAa,IAAI;AAC3F,UAAI,KAAK,SAAS,MAAM,KAAK,aAAa,IAAI,KAAK,SAAS,MAAM,KAAK,aAAa,EAAG,MAAK,SAAS,MAAM,KAAK,aAAa,IAAI,KAAK,SAAS,MAAM,KAAK,aAAa;AAAA,IACzK;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa,GAAG;AAC9B,UAAI,KAAK,eAAe,KAAK,qBAAqB;AAChD,aAAK,SAAS,OAAO,KAAK,aAAa,IAAI,KAAK,KAAK,SAAS,OAAO,KAAK,aAAa,GAAG,KAAK,SAAS,MAAM,KAAK,aAAa,GAAG,KAAK,IAAI;AAAA,MAC9I,OAAO;AACL,YAAI,KAAK,SAAS,OAAO,KAAK,aAAa,IAAI,KAAK,SAAS,MAAM,KAAK,aAAa,GAAG;AACtF,eAAK,UAAU,KAAK,SAAS,OAAO,KAAK,aAAa,GAAG,KAAK,SAAS,MAAM,KAAK,aAAa,CAAC;AAAA,QAClG,WAAW,KAAK,SAAS,OAAO,IAAI,GAAG;AACrC,eAAK,UAAU,KAAK,SAAS,OAAO,KAAK,aAAa,GAAG,CAAC;AAAA,QAC5D,OAAO;AACL,eAAK,UAAU,KAAK,SAAS,OAAO,KAAK,aAAa,GAAG,KAAK,SAAS,MAAM,KAAK,aAAa,CAAC;AAAA,QAClG;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe;AAC7B,UAAI,KAAK,SAAS,MAAM,IAAI,KAAK,SAAS,OAAO,GAAG;AAClD,YAAI,KAAK,SAAS,cAAc,QAAQ;AACtC,eAAK,SAAS,YAAY;AAAA,QAC5B;AAAA,MACF,WAAW,KAAK,SAAS,MAAM,IAAI,KAAK,SAAS,OAAO,GAAG;AACzD,YAAI,KAAK,SAAS,cAAc,MAAM;AACpC,eAAK,SAAS,YAAY;AAAA,QAC5B;AAAA,MACF;AAEA,UAAI,KAAK,SAAS,MAAM,IAAI,KAAK,SAAS,OAAO,GAAG;AAClD,YAAI,KAAK,SAAS,cAAc,SAAS;AACvC,eAAK,SAAS,YAAY;AAAA,QAC5B;AAAA,MACF,WAAW,KAAK,SAAS,MAAM,IAAI,KAAK,SAAS,OAAO,GAAG;AACzD,YAAI,KAAK,SAAS,cAAc,QAAQ;AACtC,eAAK,SAAS,YAAY;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW;AACzB,UAAI,KAAK,SAAS,MAAM,KAAK,aAAa,KAAK,KAAK,SAAS,OAAO,KAAK,aAAa,GAAG;AACvF,aAAK,SAAS,SAAS,KAAK,SAAS,MAAM,KAAK,aAAa,IAAI,KAAK,SAAS,OAAO,KAAK,aAAa,KAAK,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,OAAO;AAAA,MACpJ,OAAO;AACL,aAAK,SAAS,QAAQ;AAAA,MACxB;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB;AAC9B,WAAK,YAAY,SAAS,cAAc,MAAM;AAC9C,WAAK,iBAAiB,SAAS,cAAc,MAAM;AACnD,WAAK,UAAU,UAAU,IAAI,GAAG,OAAO,KAAK,cAAc,CAAC;AAC3D,WAAK,eAAe,UAAU,IAAI,GAAG,OAAO,KAAK,gBAAgB,QAAQ,CAAC;AAC1E,WAAK,UAAU,OAAO,KAAK,cAAc;AAEzC,UAAI,KAAK,oBAAoB;AAC3B,aAAK,mBAAmB,OAAO,KAAK,SAAS;AAAA,MAC/C,OAAO;AACL,iBAAS,KAAK,OAAO,KAAK,SAAS;AAAA,MACrC;AAGA,WAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,WAAK,mBAAmB,KAAK,iBAAiB,KAAK,IAAI;AACvD,WAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,WAAK,eAAe,iBAAiB,aAAa,KAAK,YAAY;AACnE,aAAO,iBAAiB,WAAW,KAAK,gBAAgB;AACxD,aAAO,iBAAiB,aAAa,KAAK,aAAa;AAEvD,WAAK,eAAe;AAEpB,UAAI,KAAK,aAAa,cAAc;AAClC,YAAI,KAAK,SAAS,MAAM,IAAI,KAAK,eAAe,KAAK,aAAa;AAChE;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,KAAK,SAAS,MAAM,IAAI,KAAK,gBAAgB,KAAK,cAAc;AAClE;AAAA,QACF;AAAA,MACF;AAEA,WAAK,eAAe;AACpB,WAAK,eAAe,KAAK,UAAU,sBAAsB;AACzD,WAAK,kBAAkB,KAAK,aAAa;AACzC,WAAK,iBAAiB,KAAK,aAAa;AAExC,UAAI,KAAK,cAAc,cAAc;AACnC,aAAK,eAAe,MAAM,QAAQ,GAAG,OAAO,KAAK,iBAAiB,KAAK,kBAAkB,KAAK,SAAS,MAAM,IAAI,KAAK,iBAAiB,IAAI;AAAA,MAC7I,OAAO;AACL,aAAK,eAAe,MAAM,SAAS,GAAG,OAAO,KAAK,kBAAkB,KAAK,mBAAmB,KAAK,SAAS,MAAM,IAAI,KAAK,kBAAkB,IAAI;AAAA,MACjJ;AAEA,WAAK,oBAAoB,KAAK,eAAe,sBAAsB;AACnE,WAAK,iBAAiB;AAAA,QACpB,GAAG,KAAK,iBAAiB,KAAK,kBAAkB;AAAA,QAChD,GAAG,KAAK,kBAAkB,KAAK,kBAAkB;AAAA,MACnD;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,kBAAkB;AAChC,WAAK,eAAe;AAEpB,UAAI,KAAK,aAAa,cAAc;AAClC,YAAI,KAAK,SAAS,MAAM,IAAI,KAAK,eAAe,KAAK,aAAa;AAChE;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,KAAK,SAAS,MAAM,IAAI,KAAK,gBAAgB,KAAK,cAAc;AAClE;AAAA,QACF;AAAA,MACF;AAEA,WAAK,eAAe;AACpB,WAAK,eAAe,KAAK,UAAU,sBAAsB;AACzD,WAAK,kBAAkB,KAAK,aAAa;AACzC,WAAK,iBAAiB,KAAK,aAAa;AAExC,UAAI,KAAK,cAAc,cAAc;AACnC,aAAK,eAAe,MAAM,QAAQ,GAAG,OAAO,KAAK,iBAAiB,KAAK,kBAAkB,KAAK,SAAS,MAAM,IAAI,KAAK,iBAAiB,IAAI;AAAA,MAC7I,OAAO;AACL,aAAK,eAAe,MAAM,SAAS,GAAG,OAAO,KAAK,kBAAkB,KAAK,mBAAmB,KAAK,SAAS,MAAM,IAAI,KAAK,kBAAkB,IAAI;AAAA,MACjJ;AAEA,WAAK,oBAAoB,KAAK,eAAe,sBAAsB;AACnE,WAAK,iBAAiB;AAAA,QACpB,GAAG,KAAK,iBAAiB,KAAK,kBAAkB;AAAA,QAChD,GAAG,KAAK,kBAAkB,KAAK,kBAAkB;AAAA,MACnD;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,mBAAmB;AACjC,WAAK,eAAe,oBAAoB,aAAa,KAAK,YAAY;AACtE,aAAO,oBAAoB,WAAW,KAAK,gBAAgB;AAC3D,aAAO,oBAAoB,aAAa,KAAK,aAAa;AAC1D,WAAK,UAAU,OAAO;AAAA,IACxB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa,GAAG;AAC9B,WAAK,sBAAsB;AAC3B,WAAK,YAAY;AACjB,WAAK,KAAK,UAAU,OAAO,KAAK,cAAc;AAC9C,WAAK,KAAK,UAAU,IAAI,KAAK,aAAa;AAAA,IAC5C;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB,GAAG;AAClC,WAAK,sBAAsB;AAE3B,UAAI,KAAK,aAAa;AACpB,aAAK,KAAK,UAAU,IAAI,KAAK,cAAc;AAAA,MAC7C;AAEA,WAAK,KAAK,UAAU,OAAO,KAAK,aAAa;AAAA,IAC/C;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc,GAAG;AAC/B,UAAI,SAAS;AAEb,UAAI,KAAK,qBAAqB;AAC5B,8BAAsB,WAAY;AAChC,cAAI,KAAK,EAAE,UAAU,OAAO,aAAa,QAAQ,MAAM,OAAO,iBAAiB,OAAO,SAAS,MAAM,IAAI;AACzG,cAAI,KAAK,EAAE,UAAU,OAAO,aAAa,OAAO,MAAM,OAAO,kBAAkB,OAAO,SAAS,MAAM,IAAI;AAEzG,cAAI,IAAI,KAAK,IAAI,OAAO,SAAS,MAAM,GAAG;AACxC,mBAAO,SAAS,MAAM,IAAI;AAAA,UAC5B;AAEA,cAAI,IAAI,KAAK,IAAI,OAAO,SAAS,MAAM,GAAG;AACxC,mBAAO,SAAS,MAAM,IAAI;AAAA,UAC5B;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,UAAI,SAAS;AAEb,WAAK,MAAM,CAAC;AACZ,WAAK,mBAAmB,CAAC;AAEzB,UAAI,MAAM,KAAK,GAAG,iBAAiB,SAAS,OAAO,KAAK,MAAM,GAAG,CAAC;AAClE,UAAI,QAAQ,SAAU,IAAI,OAAO;AAE/B,YAAI,gBAAgB,WAAW,EAAE;AACjC,YAAI,UAAU,OAAO,QAAQ,OAAO,QAAQ,EAAE,IAAI,SAAU,OAAO;AACjE,cAAI,QAAQ,eAAe,OAAO,CAAC,GAC/B,MAAM,MAAM,CAAC,GACbI,WAAU,MAAM,CAAC;AAErB,iBAAOA;AAAA,QACT,CAAC,EAAE,KAAK,SAAUA,UAAS;AACzB,iBAAO,cAAc,SAASA,SAAQ,EAAE;AAAA,QAC1C,CAAC;AACD,YAAI,KAAK,GAAG,QAAQ,OAAO,OAAO,OAAO,KAAK,OAAO,OAAO;AAC5D,YAAI,KAAK,OAAO,GAAG,QAAQ,OAAO,OAAO,IAAI,MAAM,WAAW,GAAG,QAAQ,OAAO,OAAO,IAAI,IAAI,OAAO;AACtG,YAAI;AACJ,YAAI;AACJ,YAAI,SAAS,GAAG,QAAQ,OAAO,OAAO,QAAQ;AAC9C,YAAI,OAAO,GAAG,QAAQ,OAAO,OAAO,MAAM;AAC1C,YAAI,WAAW,GAAG,QAAQ,OAAO,OAAO,UAAU;AAClD,YAAI,QAAQ,GAAG,QAAQ,OAAO,OAAO,OAAO;AAC5C,YAAI,YAAY,GAAG,QAAQ,OAAO,OAAO,WAAW;AACpD,YAAI,SAAS,OAAO,GAAG,QAAQ,OAAO,OAAO,QAAQ,MAAM;AAC3D,YAAI,QAAQ,GAAG,QAAQ,OAAO,OAAO,OAAO,IAAI,WAAW,GAAG,QAAQ,OAAO,OAAO,OAAO,CAAC,IAAI,KAAK;AACrG,YAAI,SAAS,OAAO,GAAG,QAAQ,OAAO,OAAO,QAAQ,MAAM,WAAW,GAAG,QAAQ,OAAO,OAAO,QAAQ,EAAE,MAAM,GAAG,IAAI,OAAO;AAC7H,YAAI,SAAS,GAAG,QAAQ,OAAO,OAAO,QAAQ;AAC9C,YAAI;AAEJ,YAAI,WAAW,QAAW;AACxB,qBAAW,SAAS,cAAc,GAAG,OAAO,MAAM,CAAC;AAAA,QACrD,OAAO;AACL,qBAAW;AAAA,QACb;AAEA,YAAI,cAAc,SAAS,sBAAsB;AAEjD,YAAI,YAAY,MAAM;AACpB,gBAAM,YAAY,MAAM,OAAO,SAAS,OAAO,IAAI,aAAa,QAAQ,EAAE;AAC1E,iBAAO,YAAY,OAAO,OAAO,SAAS,OAAO,IAAI,aAAa,QAAQ,EAAE;AAAA,QAC9E,OAAO;AACL,cAAI,CAAC,QAAQ,QAAQ;AACnB,kBAAM,YAAY,MAAM,aAAa,QAAQ,EAAE,EAAE,IAAI,aAAa,QAAQ,EAAE;AAC5E,mBAAO,YAAY,OAAO,aAAa,QAAQ,EAAE,EAAE,IAAI,aAAa,QAAQ,EAAE;AAAA,UAChF,OAAO;AACL,kBAAM,YAAY,MAAM,OAAO,SAAS,OAAO,IAAI,aAAa,QAAQ,EAAE;AAC1E,mBAAO,YAAY,OAAO,OAAO,SAAS,OAAO,IAAI,aAAa,QAAQ,EAAE;AAAA,UAC9E;AAAA,QACF;AAEA,YAAI,SAAS,MAAM,SAAS;AAC5B,YAAI,QAAQ,OAAO,SAAS;AAC5B,YAAI,SAAS;AAAA,UACX,IAAI,QAAQ,QAAQ,IAAI;AAAA,UACxB,IAAI,SAAS,OAAO,IAAI;AAAA,QAC1B;AAEA,YAAI,QAAQ;AACV,cAAI,QAAQ,GAAG,sBAAsB;AACrC,cAAI,QAAQ,MAAM;AAClB,cAAI,SAAS,MAAM;AACnB,cAAI,aAAa;AAAA,YACf,GAAG,SAAS;AAAA,YACZ,GAAG,QAAQ;AAAA,UACb;AACA,iBAAO,OAAO;AACd,kBAAQ,OAAO;AACf,mBAAS,QAAQ,SAAS,eAAe,GAAG,eAAe,WAAW,OAAO,aAAa;AAC1F,kBAAQ,SAAS,SAAS,cAAc,GAAG,cAAc,WAAW,OAAO,aAAa;AACxF,mBAAS;AAAA,YACP,IAAI,QAAQ,QAAQ,IAAI;AAAA,YACxB,IAAI,SAAS,OAAO,IAAI;AAAA,UAC1B;AAAA,QACF;AAEA,YAAI,UAAU,SAAS;AACrB,mBAAS;AAAA,QACX,WAAW,UAAU,QAAW;AAC9B,mBAAS;AAAA,QACX,OAAO;AACL,mBAAS,OAAO;AAAA,QAClB;AAEA,YAAI,iBAAiB,CAAC,GAAG,CAAC;AAE1B,YAAI,QAAQ;AACV,cAAI,OAAO,cAAc,cAAc;AACrC,qBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,kBAAI,OAAO,OAAO,CAAC,KAAK,UAAU;AAChC,oBAAI,OAAO,CAAC,EAAE,SAAS,GAAG,GAAG;AAC3B,iCAAe,CAAC,IAAI,SAAS,OAAO,CAAC,EAAE,QAAQ,KAAK,EAAE,IAAI,OAAO,cAAc,GAAG;AAAA,gBACpF,OAAO;AACL,iCAAe,CAAC,IAAI,SAAS,OAAO,CAAC,CAAC;AAAA,gBACxC;AAAA,cACF,OAAO;AACL,+BAAe,CAAC,IAAI,OAAO,CAAC;AAAA,cAC9B;AAAA,YACF;AAEA,mBAAO,OAAO,eAAe,CAAC;AAC9B,oBAAQ,QAAQ,eAAe,CAAC;AAAA,UAClC,OAAO;AACL,qBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,kBAAI,OAAO,OAAO,CAAC,KAAK,UAAU;AAChC,oBAAI,OAAO,CAAC,EAAE,SAAS,GAAG,GAAG;AAC3B,iCAAe,CAAC,IAAI,SAAS,OAAO,CAAC,EAAE,QAAQ,KAAK,EAAE,IAAI,OAAO,eAAe,GAAG;AAAA,gBACrF,OAAO;AACL,iCAAe,CAAC,IAAI,SAAS,OAAO,CAAC,CAAC;AAAA,gBACxC;AAAA,cACF,OAAO;AACL,+BAAe,CAAC,IAAI,OAAO,CAAC;AAAA,cAC9B;AAAA,YACF;AAEA,kBAAM,MAAM,eAAe,CAAC;AAC5B,qBAAS,SAAS,eAAe,CAAC;AAAA,UACpC;AAAA,QACF;AAEA,YAAI,WAAW;AAAA,UACb;AAAA,UACA;AAAA,UACA,SAAS;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU;AAAA,UACV;AAAA,UACA,QAAQ;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,UACR;AAAA,UACA;AAAA,QACF;AACA,eAAO,IAAI,EAAE,IAAI;AAEjB,YAAI,GAAG,UAAU,SAAS,EAAE,GAAG;AAC7B,iBAAO,UAAU,OAAO,IAAI,EAAE,GAAG,EAAE;AAAA,QACrC;AAEA,YAAI,UAAU,SAAS,QAAQ;AAC7B,iBAAO,iBAAiB,EAAE,IAAI;AAAA,QAChC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,UAAI,SAAS;AAEb,WAAK,WAAW,CAAC;AACjB,UAAI,WAAW,KAAK,GAAG,iBAAiB,SAAS,OAAO,KAAK,MAAM,WAAW,CAAC;AAE/E,UAAI,SAAS,WAAW,GAAG;AACzB,mBAAW,CAAC,KAAK,EAAE;AAAA,MACrB;AAEA,eAAS,QAAQ,SAAU,SAAS,OAAO;AACzC,YAAI,KAAK,OAAO,QAAQ,QAAQ,OAAO,OAAO,IAAI,MAAM,WAAW,QAAQ,QAAQ,OAAO,OAAO,IAAI,IAAI,YAAY;AACrH,YAAI,aAAa,QAAQ,sBAAsB;AAC/C,YAAI,SAAS;AAAA,UACX,GAAG,WAAW,OAAO,OAAO,aAAa,MAAM,aAAa,OAAO,EAAE;AAAA,UACrE,GAAG,WAAW,MAAM,OAAO,cAAc,MAAM,aAAa,OAAO,EAAE;AAAA,QACvE;AACA,YAAI,QAAQ;AAAA,UACV,GAAG,OAAO,IAAI,WAAW,QAAQ,OAAO,aAAa;AAAA,UACrD,GAAG,OAAO,IAAI,WAAW,SAAS,OAAO,cAAc;AAAA,QACzD;AACA,YAAI,aAAa,OAAO,QAAQ,QAAQ,OAAO,OAAO,YAAY,MAAM;AACxE,gBAAQ,aAAa,0BAA0B,EAAE;AACjD,YAAI,gBAAgB;AAAA,UAClB,IAAI;AAAA,UACJ;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,UACR;AAAA,UACA;AAAA,QACF;AACA,eAAO,SAAS,EAAE,IAAI;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,SAAS,GAAG,GAAG,OAAO;AAC9C,UAAIC;AAEJ,UAAI,CAAC,OAAO;AACV,QAAAA,aAAY,6CAA6C,OAAO,GAAG,GAAG,EAAE,OAAO,GAAG,OAAO;AAAA,MAC3F,OAAO;AACL,YAAI,QAAQ,aAAa,OAAO;AAChC,YAAI,QAAQ,KAAK,MAAM,GAAG,GAAG,KAAK;AAClC,YAAI,QAAQ,KAAK,MAAM,GAAG,GAAG,KAAK;AAClC,QAAAA,aAAY,6CAA6C,OAAO,OAAO,GAAG,EAAE,OAAO,OAAO,OAAO;AAAA,MACnG;AAEA,cAAQ,MAAM,kBAAkBA;AAChC,cAAQ,MAAM,cAAcA;AAC5B,cAAQ,MAAM,YAAYA;AAAA,IAC5B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,kBAAkB,UAAU;AAC1C,UAAI,SAAS;AAEb,UAAI,iBAAiB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACzF,UAAI,cAAc,KAAK,SAAS,OAAO,IAAI,KAAK;AAChD,UAAI,eAAe,KAAK,SAAS,OAAO,IAAI,KAAK;AACjD,UAAI,eAAe;AAAA,QACjB,GAAG,KAAK,SAAS,OAAO,IAAI,KAAK,aAAa;AAAA,QAC9C,GAAG,KAAK,SAAS,OAAO,IAAI,KAAK,aAAa;AAAA,MAChD;AACA,aAAO,QAAQ,KAAK,gBAAgB,EAAE,QAAQ,SAAU,OAAO;AAC7D,YAAI,QAAQ,eAAe,OAAO,CAAC,GAC/B,IAAI,MAAM,CAAC,GACX,UAAU,MAAM,CAAC;AAErB,YAAI,oBAAoB;AAExB,YAAI,UAAU;AACZ,8BAAoB;AAAA,QACtB;AAEA,YAAI,QAAQ,UAAU,gBAAgB;AACpC,kBAAQ,QAAQ,UAAU;AAAA,YACxB,KAAK;AACH,kCAAoB,OAAO,SAAS,OAAO,OAAO,aAAa,IAAI,CAAC,QAAQ;AAC5E;AAAA,YAEF,KAAK;AACH,mCAAqB,eAAe,QAAQ,OAAO,CAAC,QAAQ;AAC5D;AAAA,YAEF,KAAK;AACH,mCAAqB,OAAO,SAAS,MAAM,OAAO,aAAa,IAAI,eAAe,OAAO,gBAAgB,QAAQ;AACjH;AAAA,YAEF,KAAK;AACH,kCAAoB,OAAO,SAAS,OAAO,OAAO,aAAa,IAAI,CAAC,QAAQ;AAC5E;AAAA,YAEF,KAAK;AACH,mCAAqB,cAAc,QAAQ,QAAQ,CAAC,QAAQ;AAC5D;AAAA,YAEF,KAAK;AACH,mCAAqB,OAAO,SAAS,MAAM,OAAO,aAAa,IAAI,cAAc,OAAO,gBAAgB,QAAQ;AAChH;AAAA,YAEF;AACE,mCAAqB,aAAa,OAAO,aAAa,IAAI,QAAQ,OAAO,OAAO,aAAa,KAAK,CAAC,QAAQ;AAC3G;AAAA,UACJ;AAAA,QACF;AAEA,YAAI,QAAQ,QAAQ;AAClB,cAAI,QAAQ,QAAQ;AAClB,gBAAI,OAAO,cAAc,cAAc;AACrC,kCAAoB,OAAO,SAAS,OAAO,IAAI,QAAQ,OAAO,OAAO;AAAA,YACvE,OAAO;AACL,kCAAoB,OAAO,SAAS,OAAO,IAAI,QAAQ,MAAM,OAAO;AAAA,YACtE;AAAA,UACF,OAAO;AACL,gBAAI,OAAO,cAAc,cAAc;AACrC,kBAAI,OAAO,SAAS,OAAO,IAAI,QAAQ,OAAO,OAAO,cAAc,OAAO,SAAS,OAAO,IAAI,QAAQ,OAAO,OAAO,aAAa,GAAG;AAClI,oCAAoB;AAAA,cACtB,WAAW,OAAO,SAAS,OAAO,IAAI,QAAQ,SAAS,OAAO,SAAS,OAAO,IAAI,QAAQ,QAAQ,KAAK;AACrG,oCAAoB,QAAQ,QAAQ,QAAQ,OAAO,OAAO;AAAA,cAC5D,OAAO;AACL,oCAAoB;AAAA,cACtB;AAAA,YACF,OAAO;AACL,kBAAI,OAAO,SAAS,OAAO,IAAI,QAAQ,MAAM,OAAO,eAAe,OAAO,SAAS,OAAO,IAAI,QAAQ,MAAM,OAAO,cAAc,GAAG;AAClI,oCAAoB;AAAA,cACtB,WAAW,OAAO,SAAS,OAAO,IAAI,QAAQ,UAAU,OAAO,SAAS,OAAO,IAAI,QAAQ,SAAS,KAAK;AACvG,oCAAoB,QAAQ,SAAS,QAAQ,MAAM,OAAO;AAAA,cAC5D,OAAO;AACL,oCAAoB;AAAA,cACtB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,sBAAsB,OAAO;AAC/B,cAAI,QAAQ,cAAc,gBAAgB,OAAO,cAAc,gBAAgB,QAAQ,cAAc,YAAY;AAC/G,mBAAO,UAAU,QAAQ,IAAI,mBAAmB,GAAG,WAAW,QAAQ,QAAQ,KAAK;AAAA,UACrF,OAAO;AACL,mBAAO,UAAU,QAAQ,IAAI,GAAG,mBAAmB,WAAW,QAAQ,QAAQ,KAAK;AAAA,UACrF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,QAAQ;AAC/B,UAAI,SAAS;AAEb,UAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEnF,UAAI,SAAS,SAAS,QAAQ,MAAM,KAAK;AAEzC,UAAI,WAAW,CAAC,MAAM,SAAS,QAAQ,QAAQ,CAAC,IAAI,SAAS,QAAQ,QAAQ,IAAI;AAEjF,UAAI,SAAS,QAAQ,UAAU,CAAC,MAAM,GAAK,MAAM,CAAG;AAEpD,UAAI,cAAc,QAAQ,cAAc,OAAO;AAE/C,UAAI,WAAW,QAAQ,WAAW,QAAQ,WAAW;AAErD,eAAS,MAAM,MAAM,QAAQ,mBAAmB,MAAM,CAAC;AAEvD,UAAI,OAAO,WAAW,UAAU;AAE9B,YAAI,WAAW,OAAO;AACpB,mBAAS;AAAA,QACX,WAAW,WAAW,UAAU;AAC9B,mBAAS,KAAK,SAAS,MAAM;AAAA,QAC/B,WAAW,WAAW,QAAQ;AAC5B,mBAAS;AAAA,QACX,WAAW,WAAW,SAAS;AAC7B,mBAAS,KAAK,SAAS,MAAM;AAAA,QAC/B,OAAO;AACL,mBAAS,SAAS,cAAc,MAAM;AAEtC,cAAI,CAAC,QAAQ;AACX;AAAA,UACF;AAAA,QACF;AAAA,MACF,WAAW,OAAO,WAAW,UAAU;AAErC,iBAAS,SAAS,MAAM;AAAA,MAC1B,WAAW,UAAU,OAAO,QAAS;AAAA,WAAO;AAC1C,gBAAQ,KAAK,iCAAiC;AAC9C;AAAA,MACF;AAGA,UAAI,OAAO,WAAW,UAAU;AAE9B,YAAI,gBAAgB,WAAW,MAAM,EAAE,SAAS,KAAK,EAAE;AAEvD,YAAI,CAAC,eAAe;AAElB;AAAA,QACF;AAGA,YAAI,YAAY,OAAO,sBAAsB;AAC7C,YAAI,YAAY,UAAU;AAC1B,YAAI,aAAa,UAAU;AAE3B,YAAI,gBAAgB,WAAW,MAAM;AACrC,YAAI,gBAAgB,cAAc,KAAK,SAAU,WAAW;AAC1D,iBAAO,OAAO,QAAQ,OAAO,QAAQ,EACpC,IAAI,SAAU,OAAO;AACpB,gBAAI,QAAQ,eAAe,OAAO,CAAC,GAC/B,MAAM,MAAM,CAAC,GACb,UAAU,MAAM,CAAC;AAErB,mBAAO;AAAA,UACT,CAAC,EACA,KAAK,SAAU,SAAS;AACvB,mBAAO,QAAQ,MAAM;AAAA,UACvB,CAAC;AAAA,QACH,CAAC;AACD,YAAI,sBAAsB;AAE1B,YAAI,eAAe;AACjB,gCAAsB,aAAa,aAAa,EAAE,KAAK,aAAa;AAAA,QACtE,OAAO;AAEL,gCAAsB,CAAC,KAAK,SAAS,OAAO,KAAK,aAAa;AAAA,QAChE;AAGA,YAAI,KAAK,cAAc,cAAc;AACnC,mBAAS,aAAa,SAAS;AAAA,QACjC,OAAO;AACL,mBAAS,YAAY,SAAS;AAAA,QAChC;AAAA,MACF,OAAO;AACL,iBAAS,SAAS;AAAA,MACpB;AAKA,UAAI,cAAc,WAAW,KAAK,SAAS,MAAM,KAAK,aAAa,CAAC;AACpE,UAAI,eAAe,KAAK,IAAI,GAAG,KAAK,IAAI,QAAQ,KAAK,SAAS,MAAM,KAAK,aAAa,CAAC,CAAC;AAExF,UAAI,aAAa,eAAe;AAEhC,UAAI,SAAS,SAASC,QAAO,GAAG;AAC9B,YAAI,aAAa;AACf,cAAI,OAAO,cAAc,cAAc;AACrC,mBAAO,UAAU,cAAc,aAAa,GAAG,OAAO,SAAS,MAAM,CAAC;AAAA,UACxE,OAAO;AACL,mBAAO,UAAU,OAAO,SAAS,MAAM,GAAG,cAAc,aAAa,CAAC;AAAA,UACxE;AAAA,QACF,OAAO;AACL,iBAAO,SAAS,MAAM,OAAO,aAAa,IAAI,cAAc,aAAa;AAAA,QAC3E;AAAA,MACF;AAGA,WAAK,kBAAkB;AAEvB,WAAK,cAAc;AAEnB,WAAK,eAAe;AAGpB,UAAI,QAAQ,KAAK,IAAI;AAErB,UAAI,OAAO,SAASC,QAAO;AACzB,YAAI,KAAK,KAAK,IAAI,IAAI,SAAS;AAE/B,YAAI,IAAI,GAAG;AAET,iBAAO,CAAC;AACR,iBAAO,kBAAkB;AACzB,cAAI,YAAY,EAAG,QAAO,OAAO;AACjC,cAAI,SAAU,UAAS;AAAA,QACzB,OAAO;AACL,iBAAO,cAAc,sBAAsBA,KAAI;AAC/C,iBAAO,OAAO,CAAC,CAAC;AAAA,QAClB;AAAA,MACF;AAEA,WAAK;AAAA,IACP;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,WAAK,eAAe;AACpB,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,WAAK,eAAe;AACpB,WAAK,aAAa;AAClB,WAAK,kBAAkB,IAAI;AAC3B,WAAK,gBAAgB;AACrB,WAAK,YAAY,IAAI;AAAA,IACvB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,WAAK,OAAO;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa;AAC3B,WAAK,OAAO;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,GAAG,GAAG;AAC9B,WAAK,WAAW,eAAe,eAAe,CAAC,GAAG,KAAK,QAAQ,GAAG,CAAC,GAAG;AAAA,QACpE,QAAQ;AAAA,UACN;AAAA,UACA;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,QACA,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACxB,WAAK,gBAAgBP,UAAS,SAAS,GAAG,WAAW,IAAI,EAAE,KAAK,IAAI;AAEpE,WAAK,cAAc;AACnB,WAAK,KAAK,UAAU,OAAO,KAAK,WAAW;AAC3C,WAAK,GAAG,QAAQ;AAChB,WAAK,iBAAiB;AACtB,aAAO,oBAAoB,WAAW,KAAK,UAAU,KAAK;AAAA,IAC5D;AAAA,EACF,CAAC,CAAC;AAEF,SAAOA;AACT,EAAE,QAAQ;AAEV,IAAI,SAAsB,WAAY;AACpC,WAASQ,UAAS;AAChB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEnF,oBAAgB,MAAMA,OAAM;AAE5B,SAAK,UAAU;AAEf,WAAO,OAAO,MAAM,UAAU,OAAO;AACrC,SAAK,aAAa,SAAS;AAC3B,QAAI,QAAQ,WAAY,QAAO,OAAO,KAAK,YAAY,QAAQ,UAAU;AACzE,SAAK,SAAS,SAAS;AACvB,QAAI,QAAQ,OAAQ,QAAO,OAAO,KAAK,QAAQ,QAAQ,MAAM;AAC7D,QAAI,CAAC,KAAK,UAAU,KAAK,aAAa,aAAc,SAAQ,KAAK,mEAAmE;AACpI,QAAI,CAAC,KAAK,OAAO,UAAU,KAAK,OAAO,aAAa,aAAc,SAAQ,KAAK,4EAA4E;AAC3J,QAAI,CAAC,KAAK,WAAW,UAAU,KAAK,WAAW,aAAa,aAAc,SAAQ,KAAK,gFAAgF;AACvK,SAAK,KAAK;AAAA,EACZ;AAEA,eAAaA,SAAQ,CAAC;AAAA,IACpB,KAAK;AAAA,IACL,OAAO,SAAS,OAAO;AACrB,WAAK,QAAQ,WAAW,2DAA2D,KAAK,UAAU,SAAS,KAAK,UAAU,aAAa,cAAc,UAAU,iBAAiB,KAAK,OAAO,aAAa,KAAK,OAAO;AACrN,WAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,OAAO,cAAc,KAAK,OAAO;AAElF,UAAI,KAAK,UAAU,CAAC,KAAK,QAAQ,YAAY,KAAK,OAAO,UAAU,KAAK,QAAQ,YAAY,KAAK,WAAW,UAAU,KAAK,QAAQ,YAAY,CAAC,KAAK,QAAQ,UAAU;AACrK,aAAK,SAAS,IAAI,WAAW,KAAK,OAAO;AAAA,MAC3C,OAAO;AACL,aAAK,SAAS,IAAI,WAAW,KAAK,OAAO;AAAA,MAC3C;AAEA,WAAK,OAAO,KAAK;AAEjB,UAAI,OAAO,SAAS,MAAM;AAExB,YAAI,KAAK,OAAO,SAAS,KAAK,MAAM,GAAG,OAAO,SAAS,KAAK,MAAM;AAClE,YAAI,SAAS,SAAS,eAAe,EAAE;AAEvC,YAAI,OAAQ,MAAK,OAAO,SAAS,MAAM;AAAA,MACzC;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,WAAK,OAAO,OAAO;AAAA,IACrB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,WAAK,OAAO,YAAY;AAAA,IAC1B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,OAAO;AACrB,WAAK,OAAO,WAAW;AAAA,IACzB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,QAAQ,SAAS;AACxC,WAAK,OAAO,SAAS,QAAQ,OAAO;AAAA,IACtC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,GAAG,GAAG;AAC9B,WAAK,OAAO,UAAU,GAAG,CAAC;AAAA,IAC5B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG,OAAO,MAAM;AAC9B,WAAK,OAAO,UAAU,OAAO,IAAI;AAAA,IACnC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,IAAI,OAAO,MAAM;AAC/B,WAAK,OAAO,YAAY,OAAO,IAAI;AAAA,IACrC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACxB,WAAK,OAAO,QAAQ;AAAA,IACtB;AAAA,EACF,CAAC,CAAC;AAEF,SAAOA;AACT,EAAE;AAEF,IAAI,SAAsB,WAAY;AACpC,WAASC,UAAS;AAChB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEnF,oBAAgB,MAAMA,OAAM;AAE5B,SAAK,UAAU;AAEf,WAAO,OAAO,MAAM,UAAU,OAAO;AACrC,SAAK,aAAa,SAAS;AAC3B,QAAI,QAAQ,WAAY,QAAO,OAAO,KAAK,YAAY,QAAQ,UAAU;AACzE,SAAK,SAAS,SAAS;AACvB,QAAI,QAAQ,OAAQ,QAAO,OAAO,KAAK,QAAQ,QAAQ,MAAM;AAC7D,SAAK,KAAK;AAAA,EACZ;AAEA,eAAaA,SAAQ,CAAC;AAAA,IACpB,KAAK;AAAA,IACL,OAAO,SAAS,OAAO;AACrB,WAAK,SAAS,IAAI,WAAW,KAAK,OAAO;AACzC,WAAK,OAAO,KAAK;AAEjB,UAAI,OAAO,SAAS,MAAM;AAExB,YAAI,KAAK,OAAO,SAAS,KAAK,MAAM,GAAG,OAAO,SAAS,KAAK,MAAM;AAClE,YAAI,SAAS,SAAS,eAAe,EAAE;AAEvC,YAAI,OAAQ,MAAK,OAAO,SAAS,MAAM;AAAA,MACzC;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,WAAK,OAAO,OAAO;AAAA,IACrB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,WAAK,OAAO,YAAY;AAAA,IAC1B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,OAAO;AACrB,WAAK,OAAO,WAAW;AAAA,IACzB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,QAAQ,SAAS;AACxC,WAAK,OAAO,SAAS,QAAQ,OAAO;AAAA,IACtC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,GAAG,GAAG;AAC9B,WAAK,OAAO,UAAU,GAAG,CAAC;AAAA,IAC5B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG,OAAO,MAAM;AAC9B,WAAK,OAAO,UAAU,OAAO,IAAI;AAAA,IACnC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,IAAI,OAAO,MAAM;AAC/B,WAAK,OAAO,YAAY,OAAO,IAAI;AAAA,IACrC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACxB,WAAK,OAAO,QAAQ;AAAA,IACtB;AAAA,EACF,CAAC,CAAC;AAEF,SAAOA;AACT,EAAE;AAEF,IAAO,gCAAQ;", "names": ["_getPrototypeOf", "o", "_setPrototypeOf", "p", "self", "_get", "target", "property", "receiver", "_default", "<PERSON><PERSON><PERSON><PERSON>Reached", "onScroll", "Lethargy", "section", "transform", "render", "loop", "Smooth", "Native"]}