<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="profileGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4338ca;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="400" fill="url(#profileGrad)"/>
  <circle cx="200" cy="150" r="60" fill="rgba(255,255,255,0.2)"/>
  <rect x="150" y="220" width="100" height="80" rx="10" fill="rgba(255,255,255,0.2)"/>
  <text x="200" y="320" font-family="Inter, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white"><PERSON><PERSON><PERSON></text>
  <text x="200" y="350" font-family="Inter, sans-serif" font-size="16" text-anchor="middle" fill="rgba(255,255,255,0.8)">Web Developer</text>
</svg>
