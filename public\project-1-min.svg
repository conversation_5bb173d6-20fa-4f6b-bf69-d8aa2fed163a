<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="project1Grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0099cc;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="600" height="400" fill="url(#project1Grad)"/>
  <rect x="50" y="50" width="500" height="300" rx="20" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
  <circle cx="150" cy="150" r="30" fill="rgba(255,255,255,0.2)"/>
  <circle cx="300" cy="150" r="25" fill="rgba(255,255,255,0.15)"/>
  <circle cx="450" cy="150" r="35" fill="rgba(255,255,255,0.25)"/>
  <text x="300" y="250" font-family="Inter, sans-serif" font-size="32" font-weight="bold" text-anchor="middle" fill="white">E-Commerce</text>
  <text x="300" y="290" font-family="Inter, sans-serif" font-size="32" font-weight="bold" text-anchor="middle" fill="white">Platform</text>
  <text x="300" y="330" font-family="Inter, sans-serif" font-size="16" text-anchor="middle" fill="rgba(255,255,255,0.8)">Modern shopping experience</text>
</svg>
