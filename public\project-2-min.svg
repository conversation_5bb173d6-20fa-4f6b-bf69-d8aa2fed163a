<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="project2Grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a855f7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="600" height="400" fill="url(#project2Grad)"/>
  <rect x="50" y="100" width="80" height="200" fill="rgba(255,255,255,0.2)"/>
  <rect x="150" y="150" width="80" height="150" fill="rgba(255,255,255,0.25)"/>
  <rect x="250" y="120" width="80" height="180" fill="rgba(255,255,255,0.3)"/>
  <rect x="350" y="80" width="80" height="220" fill="rgba(255,255,255,0.2)"/>
  <rect x="450" y="140" width="80" height="160" fill="rgba(255,255,255,0.25)"/>
  <text x="300" y="50" font-family="Inter, sans-serif" font-size="28" font-weight="bold" text-anchor="middle" fill="white">Data Visualization</text>
  <text x="300" y="350" font-family="Inter, sans-serif" font-size="16" text-anchor="middle" fill="rgba(255,255,255,0.8)">Interactive analytics dashboard</text>
</svg>
