<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="project3Grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0891b2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="600" height="400" fill="url(#project3Grad)"/>
  <rect x="200" y="50" width="200" height="300" rx="30" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="3"/>
  <rect x="220" y="80" width="160" height="30" rx="15" fill="rgba(255,255,255,0.2)"/>
  <circle cx="240" cy="140" r="15" fill="rgba(255,255,255,0.2)"/>
  <circle cx="280" cy="140" r="15" fill="rgba(255,255,255,0.25)"/>
  <circle cx="320" cy="140" r="15" fill="rgba(255,255,255,0.2)"/>
  <circle cx="360" cy="140" r="15" fill="rgba(255,255,255,0.15)"/>
  <rect x="220" y="180" width="160" height="120" rx="10" fill="rgba(255,255,255,0.15)"/>
  <text x="300" y="250" font-family="Inter, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="white">Mobile App</text>
  <text x="300" y="275" font-family="Inter, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="white">UI/UX</text>
</svg>
