<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="project4Grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ec4899;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#db2777;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="600" height="400" fill="url(#project4Grad)"/>
  <circle cx="300" cy="150" r="80" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="3"/>
  <circle cx="280" cy="130" r="8" fill="white"/>
  <circle cx="320" cy="130" r="8" fill="white"/>
  <path d="M 260 170 Q 300 200 340 170" stroke="white" stroke-width="4" fill="none" stroke-linecap="round"/>
  <rect x="150" y="250" width="300" height="60" rx="30" fill="rgba(255,255,255,0.1)"/>
  <text x="300" y="285" font-family="Inter, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="white">AI-Powered Chatbot</text>
  <text x="300" y="340" font-family="Inter, sans-serif" font-size="16" text-anchor="middle" fill="rgba(255,255,255,0.8)">Natural language processing</text>
</svg>
