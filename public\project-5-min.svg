<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="project5Grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0099cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0077aa;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="600" height="400" fill="url(#project5Grad)"/>
  <rect x="100" y="100" width="80" height="80" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
  <rect x="200" y="100" width="80" height="80" fill="rgba(255,255,255,0.25)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
  <rect x="300" y="100" width="80" height="80" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
  <rect x="400" y="100" width="80" height="80" fill="rgba(255,255,255,0.25)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
  <rect x="150" y="200" width="80" height="80" fill="rgba(255,255,255,0.3)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
  <rect x="250" y="200" width="80" height="80" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
  <rect x="350" y="200" width="80" height="80" fill="rgba(255,255,255,0.25)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
  <text x="300" y="50" font-family="Inter, sans-serif" font-size="28" font-weight="bold" text-anchor="middle" fill="white">Blockchain DApp</text>
  <text x="300" y="320" font-family="Inter, sans-serif" font-size="16" text-anchor="middle" fill="rgba(255,255,255,0.8)">Decentralized application</text>
</svg>
