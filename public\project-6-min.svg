<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="project6Grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="600" height="400" fill="url(#project6Grad)"/>
  <path d="M 50 300 Q 150 200 250 250 T 450 200 T 550 250" stroke="rgba(255,255,255,0.4)" stroke-width="3" fill="none"/>
  <path d="M 50 320 Q 150 220 250 270 T 450 220 T 550 270" stroke="rgba(255,255,255,0.3)" stroke-width="2" fill="none"/>
  <circle cx="150" cy="220" r="6" fill="white"/>
  <circle cx="250" cy="250" r="8" fill="white"/>
  <circle cx="350" cy="200" r="6" fill="white"/>
  <circle cx="450" cy="220" r="8" fill="white"/>
  <rect x="50" y="100" width="500" height="60" rx="10" fill="rgba(255,255,255,0.1)"/>
  <text x="300" y="140" font-family="Inter, sans-serif" font-size="28" font-weight="bold" text-anchor="middle" fill="white">Real-time Analytics</text>
  <text x="300" y="350" font-family="Inter, sans-serif" font-size="16" text-anchor="middle" fill="rgba(255,255,255,0.8)">Live data processing</text>
</svg>
