import './style.css'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import LocomotiveScroll from 'locomotive-scroll'

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger)

// ===== GLOBAL VARIABLES =====
let locoScroll
let isLoading = true

// ===== PRELOADER & LOADING ANIMATION =====
class PreloaderManager {
  constructor() {
    this.preloader = document.getElementById('preloader')
    this.progressBar = document.getElementById('progressBar')
    this.progressText = document.getElementById('progressText')
    this.preloaderText = document.querySelector('.preloader-text')
    this.main = document.getElementById('main')

    this.init()
  }

  init() {
    // Hide main content initially
    gsap.set(this.main, { opacity: 0 })

    // Animate preloader text
    gsap.fromTo(this.preloaderText,
      {
        opacity: 0,
        y: 30,
        filter: 'blur(10px)'
      },
      {
        opacity: 1,
        y: 0,
        filter: 'blur(0px)',
        duration: 1,
        ease: 'power2.out'
      }
    )

    // Start loading animation
    this.startLoading()
  }

  startLoading() {
    let progress = 0
    const duration = 3000 // 3 seconds
    const interval = 50 // Update every 50ms
    const increment = 100 / (duration / interval)

    const loadingInterval = setInterval(() => {
      progress += increment + Math.random() * 2 // Add some randomness
      progress = Math.min(progress, 100)

      // Update progress bar and text
      gsap.to(this.progressBar, {
        width: `${progress}%`,
        duration: 0.1,
        ease: 'none'
      })

      this.progressText.textContent = `${Math.floor(progress)}%`

      if (progress >= 100) {
        clearInterval(loadingInterval)
        setTimeout(() => this.completeLoading(), 500)
      }
    }, interval)
  }

  completeLoading() {
    const tl = gsap.timeline({
      onComplete: () => {
        isLoading = false
        this.preloader.style.display = 'none'
        this.initMainAnimations()
      }
    })

    tl.to(this.progressBar, {
      width: '100%',
      duration: 0.5,
      ease: 'power2.out'
    })
    .to(this.preloader, {
      opacity: 0,
      scale: 0.9,
      duration: 1,
      ease: 'power2.inOut'
    }, '+=0.5')
    .to(this.main, {
      opacity: 1,
      duration: 1,
      ease: 'power2.out'
    }, '-=0.5')
  }

  initMainAnimations() {
    // Initialize smooth scrolling
    initSmoothScroll()

    // Initialize all animations
    initHeroAnimations()
    initScrollAnimations()
    initInteractiveElements()
  }
}

// ===== SMOOTH SCROLLING =====
function initSmoothScroll() {
  locoScroll = new LocomotiveScroll({
    el: document.querySelector('[data-scroll-container]'),
    smooth: true,
    multiplier: 1,
    class: 'is-reveal'
  })

  // Update ScrollTrigger when Locomotive Scroll updates
  locoScroll.on('scroll', ScrollTrigger.update)

  // Setup ScrollTrigger to use Locomotive Scroll
  ScrollTrigger.scrollerProxy('[data-scroll-container]', {
    scrollTop(value) {
      return arguments.length ? locoScroll.scrollTo(value, 0, 0) : locoScroll.scroll.instance.scroll.y
    },
    getBoundingClientRect() {
      return { top: 0, left: 0, width: window.innerWidth, height: window.innerHeight }
    },
    pinType: document.querySelector('[data-scroll-container]').style.transform ? 'transform' : 'fixed'
  })

  // Refresh ScrollTrigger and Locomotive Scroll
  ScrollTrigger.addEventListener('refresh', () => locoScroll.update())
  ScrollTrigger.refresh()
}

// ===== HERO ANIMATIONS =====
function initHeroAnimations() {
  const heroTitle = document.querySelector('.hero-title')
  const heroSubtitle = document.querySelector('.hero-subtitle')
  const ctaButton = document.querySelector('.cta-button')
  const heroSpline = document.querySelector('.hero-spline')
  const floatingOrbs = document.querySelectorAll('.orb')

  // Create hero timeline
  const heroTl = gsap.timeline({ delay: 0.5 })

  heroTl
    .fromTo(heroTitle,
      {
        opacity: 0,
        y: 50,
        filter: 'blur(10px)'
      },
      {
        opacity: 1,
        y: 0,
        filter: 'blur(0px)',
        duration: 1.2,
        ease: 'power2.out'
      }
    )
    .fromTo(heroSubtitle,
      {
        opacity: 0,
        y: 30
      },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: 'power2.out'
      },
      '-=0.6'
    )
    .fromTo(ctaButton,
      {
        opacity: 0,
        y: 20,
        scale: 0.9
      },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        ease: 'back.out(1.7)'
      },
      '-=0.4'
    )
    .fromTo(heroSpline,
      {
        opacity: 0,
        x: 100
      },
      {
        opacity: 0.8,
        x: 0,
        duration: 1.5,
        ease: 'power2.out'
      },
      '-=1'
    )

  // Animate floating orbs
  floatingOrbs.forEach((orb, index) => {
    gsap.to(orb, {
      y: -20,
      duration: 3 + index,
      repeat: -1,
      yoyo: true,
      ease: 'power1.inOut',
      delay: index * 0.5
    })
  })

  // CTA button hover animation
  ctaButton.addEventListener('mouseenter', () => {
    gsap.to(ctaButton, {
      scale: 1.05,
      duration: 0.3,
      ease: 'power2.out'
    })
  })

  ctaButton.addEventListener('mouseleave', () => {
    gsap.to(ctaButton, {
      scale: 1,
      duration: 0.3,
      ease: 'power2.out'
    })
  })
}

// ===== SCROLL ANIMATIONS =====
function initScrollAnimations() {
  // About section animations
  const aboutImage = document.querySelector('.about-image')
  const aboutText = document.querySelector('.about-text')
  const skillItems = document.querySelectorAll('.skill-item')

  // About image animation
  ScrollTrigger.create({
    trigger: aboutImage,
    scroller: '[data-scroll-container]',
    start: 'top 80%',
    onEnter: () => {
      gsap.fromTo(aboutImage,
        { opacity: 0, x: -50 },
        { opacity: 1, x: 0, duration: 1, ease: 'power2.out' }
      )
    }
  })

  // About text animation
  ScrollTrigger.create({
    trigger: aboutText,
    scroller: '[data-scroll-container]',
    start: 'top 80%',
    onEnter: () => {
      gsap.fromTo(aboutText,
        { opacity: 0, x: 50 },
        { opacity: 1, x: 0, duration: 1, ease: 'power2.out' }
      )
    }
  })

  // Skills animation with stagger
  ScrollTrigger.create({
    trigger: '.skills-grid',
    scroller: '[data-scroll-container]',
    start: 'top 80%',
    onEnter: () => {
      gsap.fromTo(skillItems,
        { opacity: 0, y: 30 },
        {
          opacity: 1,
          y: 0,
          duration: 0.6,
          ease: 'power2.out',
          stagger: 0.1
        }
      )
    }
  })

  // Projects section animations
  const projectCards = document.querySelectorAll('.project-card')

  ScrollTrigger.create({
    trigger: '.projects-container',
    scroller: '[data-scroll-container]',
    start: 'top 80%',
    onEnter: () => {
      gsap.fromTo(projectCards,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: 'power2.out',
          stagger: 0.2
        }
      )
    }
  })

  // Contact form animations
  const formInputs = document.querySelectorAll('.form-group input, .form-group textarea')
  const submitBtn = document.querySelector('.submit-btn')
  const contactInfo = document.querySelector('.contact-info')

  ScrollTrigger.create({
    trigger: '.contact-form',
    scroller: '[data-scroll-container]',
    start: 'top 80%',
    onEnter: () => {
      gsap.fromTo(formInputs,
        { opacity: 0, x: -30 },
        {
          opacity: 1,
          x: 0,
          duration: 0.6,
          ease: 'power2.out',
          stagger: 0.1
        }
      )

      gsap.fromTo(submitBtn,
        { opacity: 0, y: 20 },
        { opacity: 1, y: 0, duration: 0.8, ease: 'power2.out', delay: 0.4 }
      )
    }
  })

  ScrollTrigger.create({
    trigger: contactInfo,
    scroller: '[data-scroll-container]',
    start: 'top 80%',
    onEnter: () => {
      gsap.fromTo(contactInfo,
        { opacity: 0, x: 30 },
        { opacity: 1, x: 0, duration: 1, ease: 'power2.out' }
      )
    }
  })

  // Footer particles animation
  const particles = document.querySelectorAll('.particle')

  ScrollTrigger.create({
    trigger: '.footer',
    scroller: '[data-scroll-container]',
    start: 'top 90%',
    onEnter: () => {
      gsap.fromTo(particles,
        { opacity: 0, y: 20 },
        {
          opacity: 0.6,
          y: 0,
          duration: 1,
          ease: 'power2.out',
          stagger: 0.1
        }
      )
    }
  })
}

// ===== INTERACTIVE ELEMENTS =====
function initInteractiveElements() {
  // Navigation functionality
  initNavigation()

  // Project cards hover effects
  initProjectCards()

  // Contact form functionality
  initContactForm()

  // Skill items hover effects
  initSkillItems()

  // Background orbs animation
  initBackgroundOrbs()
}

// Navigation
function initNavigation() {
  const nav = document.getElementById('nav')
  const navLinks = document.querySelectorAll('.nav-link')
  const hamburger = document.getElementById('navHamburger')
  const navMenu = document.getElementById('navMenu')

  // Scroll effect on navigation
  if (locoScroll) {
    locoScroll.on('scroll', (instance) => {
      if (instance.scroll.y > 100) {
        nav.classList.add('scrolled')
      } else {
        nav.classList.remove('scrolled')
      }
    })
  }

  // Mobile menu toggle
  hamburger.addEventListener('click', () => {
    const isActive = hamburger.classList.toggle('active')
    navMenu.classList.toggle('active')

    // Update ARIA attributes for accessibility
    hamburger.setAttribute('aria-expanded', isActive.toString())

    // Prevent body scroll when menu is open
    if (isActive) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }
  })

  // Smooth scroll to sections
  navLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault()
      const targetId = link.getAttribute('href').substring(1)
      const targetElement = document.getElementById(targetId)

      if (targetElement && locoScroll) {
        locoScroll.scrollTo(targetElement)

        // Close mobile menu if open
        hamburger.classList.remove('active')
        navMenu.classList.remove('active')
        hamburger.setAttribute('aria-expanded', 'false')
        document.body.style.overflow = ''

        // Update active link
        navLinks.forEach(l => l.classList.remove('active'))
        link.classList.add('active')
      }
    })
  })
}

// Project cards
function initProjectCards() {
  const projectCards = document.querySelectorAll('.project-card')

  projectCards.forEach(card => {
    const overlay = card.querySelector('.project-overlay')
    const btn = card.querySelector('.project-btn')

    card.addEventListener('mouseenter', () => {
      gsap.to(card, {
        y: -10,
        duration: 0.3,
        ease: 'power2.out'
      })
    })

    card.addEventListener('mouseleave', () => {
      gsap.to(card, {
        y: 0,
        duration: 0.3,
        ease: 'power2.out'
      })
    })

    btn.addEventListener('click', () => {
      // Add project view functionality here
      console.log('Project clicked:', card.dataset.project)
    })
  })
}

// Contact form
function initContactForm() {
  const form = document.getElementById('contactForm')
  const submitBtn = document.getElementById('submitBtn')

  form.addEventListener('submit', (e) => {
    e.preventDefault()

    // Animate submit button
    gsap.to(submitBtn, {
      scale: 0.95,
      duration: 0.1,
      yoyo: true,
      repeat: 1,
      ease: 'power2.inOut'
    })

    // Simulate form submission
    setTimeout(() => {
      alert('Message sent successfully! (This is a demo)')
      form.reset()
    }, 500)
  })

  // Form input focus effects
  const formGroups = document.querySelectorAll('.form-group')

  formGroups.forEach(group => {
    const input = group.querySelector('input, textarea')
    const label = group.querySelector('label')

    input.addEventListener('focus', () => {
      gsap.to(group, {
        scale: 1.02,
        duration: 0.2,
        ease: 'power2.out'
      })
    })

    input.addEventListener('blur', () => {
      gsap.to(group, {
        scale: 1,
        duration: 0.2,
        ease: 'power2.out'
      })
    })
  })
}

// Skill items
function initSkillItems() {
  const skillItems = document.querySelectorAll('.skill-item')

  skillItems.forEach(item => {
    item.addEventListener('mouseenter', () => {
      gsap.to(item, {
        y: -5,
        scale: 1.05,
        duration: 0.3,
        ease: 'power2.out'
      })
    })

    item.addEventListener('mouseleave', () => {
      gsap.to(item, {
        y: 0,
        scale: 1,
        duration: 0.3,
        ease: 'power2.out'
      })
    })
  })
}

// Background orbs
function initBackgroundOrbs() {
  const bgOrbs = document.querySelectorAll('.bg-orb')

  bgOrbs.forEach((orb, index) => {
    gsap.to(orb, {
      rotation: 360,
      duration: 20 + index * 5,
      repeat: -1,
      ease: 'none'
    })

    gsap.to(orb, {
      scale: 1.1,
      duration: 4 + index,
      repeat: -1,
      yoyo: true,
      ease: 'power1.inOut'
    })
  })
}

// ===== UTILITY FUNCTIONS =====
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// Handle window resize
const handleResize = debounce(() => {
  if (locoScroll) {
    locoScroll.update()
  }
  ScrollTrigger.refresh()
}, 250)

window.addEventListener('resize', handleResize)

// ===== PERFORMANCE OPTIMIZATIONS =====
// Lazy load images
function lazyLoadImages() {
  const images = document.querySelectorAll('img[data-src]')
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target
        img.src = img.dataset.src
        img.classList.remove('lazy')
        imageObserver.unobserve(img)
      }
    })
  })

  images.forEach(img => imageObserver.observe(img))
}

// Preload critical resources
function preloadCriticalResources() {
  const criticalResources = [
    '/profile-min.svg',
    '/project-1-min.svg',
    '/project-2-min.svg'
  ]

  criticalResources.forEach(resource => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'image'
    link.href = resource
    document.head.appendChild(link)
  })
}

// Optimize animations based on device capabilities
function optimizeAnimations() {
  const isLowEndDevice = navigator.hardwareConcurrency <= 2 ||
                        navigator.deviceMemory <= 2 ||
                        window.matchMedia('(prefers-reduced-motion: reduce)').matches

  if (isLowEndDevice) {
    // Reduce animation complexity
    gsap.globalTimeline.timeScale(0.5)
    document.documentElement.style.setProperty('--transition-base', '0.1s')
    document.documentElement.style.setProperty('--transition-slow', '0.2s')
  }
}

// ===== CUSTOM CURSOR =====
function initCustomCursor() {
  // Only on desktop devices
  if (window.matchMedia('(pointer: fine)').matches) {
    const cursor = document.createElement('div')
    const cursorFollower = document.createElement('div')

    cursor.className = 'cursor'
    cursorFollower.className = 'cursor-follower'

    document.body.appendChild(cursor)
    document.body.appendChild(cursorFollower)

    let mouseX = 0, mouseY = 0
    let followerX = 0, followerY = 0

    // Update cursor position
    document.addEventListener('mousemove', (e) => {
      mouseX = e.clientX
      mouseY = e.clientY

      gsap.to(cursor, {
        x: mouseX - 10,
        y: mouseY - 10,
        duration: 0.1
      })
    })

    // Smooth follower animation
    function animateFollower() {
      followerX += (mouseX - followerX) * 0.1
      followerY += (mouseY - followerY) * 0.1

      gsap.set(cursorFollower, {
        x: followerX - 20,
        y: followerY - 20
      })

      requestAnimationFrame(animateFollower)
    }
    animateFollower()

    // Hover effects
    const hoverElements = document.querySelectorAll('a, button, .project-card, .skill-item')

    hoverElements.forEach(el => {
      el.addEventListener('mouseenter', () => {
        cursor.classList.add('hover')
        cursorFollower.classList.add('hover')
      })

      el.addEventListener('mouseleave', () => {
        cursor.classList.remove('hover')
        cursorFollower.classList.remove('hover')
      })
    })
  }
}

// ===== INITIALIZE APP =====
document.addEventListener('DOMContentLoaded', () => {
  // Performance optimizations
  preloadCriticalResources()
  optimizeAnimations()

  // Add data-scroll-container to main
  const main = document.getElementById('main')
  if (main) {
    main.setAttribute('data-scroll-container', '')
  }

  // Initialize custom cursor
  initCustomCursor()

  // Initialize preloader
  new PreloaderManager()

  // Initialize lazy loading
  lazyLoadImages()
})

// Handle page visibility change
document.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    // Pause animations when tab is not visible
    gsap.globalTimeline.pause()
  } else {
    // Resume animations when tab becomes visible
    gsap.globalTimeline.resume()
  }
})

// Smooth scroll to top function
window.scrollToTop = () => {
  if (locoScroll) {
    locoScroll.scrollTo(0)
  }
}

// ===== ERROR HANDLING =====
window.addEventListener('error', (e) => {
  console.error('Portfolio Error:', e.error)
  // Graceful degradation - ensure basic functionality works
  if (e.error.message.includes('gsap') || e.error.message.includes('locomotive')) {
    document.body.classList.add('no-animations')
  }
})

// ===== DEVELOPMENT HELPERS =====
if (import.meta.env.DEV) {
  // Development mode helpers
  window.portfolioDebug = {
    locoScroll,
    gsap,
    ScrollTrigger,
    refreshAnimations: () => {
      ScrollTrigger.refresh()
      if (locoScroll) locoScroll.update()
    },
    toggleAnimations: () => {
      document.body.classList.toggle('no-animations')
    }
  }

  console.log('🚀 Portfolio loaded in development mode')
  console.log('Use window.portfolioDebug for debugging')
}

// Export for potential external use
window.portfolioApp = {
  locoScroll,
  gsap,
  ScrollTrigger,
  version: '1.0.0'
}

// ===== FINAL INITIALIZATION MESSAGE =====
console.log('✨ Nitesh Gupta Portfolio - Loaded Successfully!')
console.log('🎨 Featuring GSAP animations, Locomotive Scroll, and 3D integration')
console.log('📧 Contact: <EMAIL>')
console.log('🔗 GitHub: https://github.com/nitesh124-coder')
