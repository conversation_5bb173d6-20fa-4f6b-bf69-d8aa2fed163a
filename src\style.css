/* ===== CSS RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Color Palette - Futuristic Blues & Violets */
  --primary-bg: #0a0a0f;
  --secondary-bg: #111118;
  --accent-bg: #1a1a2e;
  --card-bg: rgba(26, 26, 46, 0.4);
  --glass-bg: rgba(255, 255, 255, 0.05);

  --primary-blue: #00d4ff;
  --secondary-blue: #0099cc;
  --accent-violet: #6366f1;
  --neon-purple: #a855f7;
  --neon-cyan: #06b6d4;
  --neon-pink: #ec4899;

  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --text-muted: rgba(255, 255, 255, 0.6);
  --text-accent: var(--primary-blue);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-blue), var(--accent-violet));
  --gradient-secondary: linear-gradient(135deg, var(--neon-purple), var(--neon-cyan));
  --gradient-accent: linear-gradient(135deg, var(--neon-cyan), var(--neon-pink));
  --gradient-bg: radial-gradient(ellipse at center, rgba(0, 212, 255, 0.1) 0%, transparent 70%);

  /* Shadows & Glows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.6);

  --glow-sm: 0 0 10px rgba(0, 212, 255, 0.3);
  --glow-md: 0 0 20px rgba(0, 212, 255, 0.4);
  --glow-lg: 0 0 30px rgba(0, 212, 255, 0.5);
  --glow-xl: 0 0 40px rgba(0, 212, 255, 0.6);

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-base: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;

  /* Z-index */
  --z-preloader: 9999;
  --z-nav: 1000;
  --z-modal: 999;
  --z-overlay: 100;
  --z-base: 1;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family);
  background: var(--primary-bg);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Smooth scrolling with Locomotive */
html.has-scroll-smooth {
  overflow: hidden;
}

html.has-scroll-dragging {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.has-scroll-smooth body {
  overflow: hidden;
}

.has-scroll-smooth [data-scroll-container] {
  min-height: 100vh;
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.section-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  text-align: center;
  margin-bottom: var(--space-16);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
}

/* Glassmorphism Effect */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
}

/* Neumorphic Button */
.neu-button {
  background: var(--secondary-bg);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-4) var(--space-6);
  color: var(--text-primary);
  font-family: var(--font-family);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-base);
  box-shadow:
    8px 8px 16px rgba(0, 0, 0, 0.4),
    -8px -8px 16px rgba(255, 255, 255, 0.02);
}

.neu-button:hover {
  box-shadow:
    4px 4px 8px rgba(0, 0, 0, 0.4),
    -4px -4px 8px rgba(255, 255, 255, 0.02),
    var(--glow-md);
  transform: translateY(-2px);
}

/* Floating Animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.float {
  animation: float 6s ease-in-out infinite;
}

/* Glow Animation */
@keyframes glow-pulse {
  0%, 100% { box-shadow: var(--glow-sm); }
  50% { box-shadow: var(--glow-lg); }
}

.glow-pulse {
  animation: glow-pulse 2s ease-in-out infinite;
}

/* ===== PRELOADER ===== */
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-preloader);
  opacity: 1;
  visibility: visible;
  transition: all 1s ease-out;
}

.preloader.hidden {
  opacity: 0;
  visibility: hidden;
}

.preloader-content {
  text-align: center;
  z-index: 2;
}

.preloader-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-bg);
  opacity: 0.3;
}

.preloader-text {
  font-size: var(--font-size-5xl);
  font-weight: 300;
  color: var(--text-primary);
  margin-bottom: var(--space-8);
  opacity: 0;
  transform: translateY(30px);
  filter: blur(10px);
}

.progress-container {
  width: 300px;
  margin: 0 auto;
}

.progress-bar {
  width: 0%;
  height: 2px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  transition: width 0.1s ease-out;
  box-shadow: var(--glow-sm);
}

.progress-text {
  margin-top: var(--space-4);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 300;
}

/* ===== NAVIGATION ===== */
.nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: var(--space-6) 0;
  background: rgba(10, 10, 15, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: var(--z-nav);
  transition: all var(--transition-base);
}

.nav.scrolled {
  padding: var(--space-4) 0;
  background: rgba(10, 10, 15, 0.95);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-logo a {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  text-decoration: none;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-menu {
  display: flex;
  gap: var(--space-8);
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 400;
  transition: all var(--transition-base);
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--text-accent);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: width var(--transition-base);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.nav-hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.nav-hamburger span {
  width: 25px;
  height: 2px;
  background: var(--text-primary);
  transition: all var(--transition-base);
}

.nav-hamburger.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.nav-hamburger.active span:nth-child(2) {
  opacity: 0;
}

.nav-hamburger.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* ===== HERO SECTION ===== */
.hero {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-spline {
  position: absolute;
  top: 0;
  right: -10%;
  width: 60%;
  height: 100%;
  opacity: 0.8;
  transform: translateX(100px);
}

.hero-spline iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.floating-orbs {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.orb {
  position: absolute;
  border-radius: 50%;
  background: var(--gradient-secondary);
  filter: blur(1px);
  opacity: 0.6;
}

.orb-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 10%;
  animation: float 8s ease-in-out infinite;
}

.orb-2 {
  width: 60px;
  height: 60px;
  top: 60%;
  left: 80%;
  animation: float 6s ease-in-out infinite reverse;
}

.orb-3 {
  width: 80px;
  height: 80px;
  top: 80%;
  left: 20%;
  animation: float 10s ease-in-out infinite;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  padding: 0 var(--space-6);
}

.hero-title {
  font-size: var(--font-size-6xl);
  font-weight: 300;
  line-height: 1.2;
  margin-bottom: var(--space-6);
  opacity: 0;
  transform: translateY(50px);
  filter: blur(10px);
}

.hero-title .highlight {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  margin-bottom: var(--space-10);
  opacity: 0;
  transform: translateY(30px);
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-5) var(--space-8);
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--radius-full);
  color: var(--text-primary);
  font-family: var(--font-family);
  font-size: var(--font-size-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-lg);
  opacity: 0;
  transform: translateY(20px) scale(0.9);
}

.cta-button:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: var(--shadow-xl), var(--glow-lg);
}

.cta-button i {
  font-size: var(--font-size-lg);
  transition: transform var(--transition-base);
}

.cta-button:hover i {
  transform: translateX(5px);
}

/* Background Elements */
.bg-elements {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.bg-orb {
  position: absolute;
  border-radius: 50%;
  background: var(--gradient-bg);
  filter: blur(40px);
  opacity: 0.3;
}

.bg-orb-1 {
  width: 400px;
  height: 400px;
  top: -200px;
  right: -200px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.3) 0%, transparent 70%);
}

.bg-orb-2 {
  width: 300px;
  height: 300px;
  bottom: -150px;
  left: -150px;
  background: radial-gradient(circle, rgba(6, 182, 212, 0.3) 0%, transparent 70%);
}

.bg-orb-3 {
  width: 200px;
  height: 200px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: radial-gradient(circle, rgba(168, 85, 247, 0.2) 0%, transparent 70%);
}

/* ===== ABOUT SECTION ===== */
.about {
  padding: var(--space-24) 0;
  position: relative;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--space-16);
  align-items: center;
}

.about-image {
  position: relative;
}

.image-container {
  position: relative;
  width: 300px;
  height: 300px;
  margin: 0 auto;
}

.profile-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.1);
  transition: all var(--transition-slow);
  filter: grayscale(20%);
}

.image-container:hover .profile-img {
  transform: scale(1.05) rotate(5deg);
  filter: grayscale(0%);
  box-shadow: var(--shadow-xl), var(--glow-lg);
}

.image-glow {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: var(--gradient-primary);
  border-radius: 50%;
  opacity: 0;
  filter: blur(20px);
  transition: opacity var(--transition-slow);
  z-index: -1;
}

.image-container:hover .image-glow {
  opacity: 0.3;
}

.about-text {
  opacity: 0;
  transform: translateX(50px);
}

.about-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  line-height: 1.8;
  margin-bottom: var(--space-10);
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-4);
}

.skill-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
  cursor: pointer;
  opacity: 0;
  transform: translateY(30px);
}

.skill-item:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow-lg), var(--glow-sm);
}

.skill-item i {
  font-size: var(--font-size-2xl);
  color: var(--text-accent);
}

.skill-item span {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

/* ===== PROJECTS SECTION ===== */
.projects {
  padding: var(--space-24) 0;
  position: relative;
}

.projects-container {
  display: flex;
  gap: var(--space-6);
  overflow-x: auto;
  padding: var(--space-4) 0;
  scroll-behavior: smooth;
}

.projects-container::-webkit-scrollbar {
  height: 8px;
}

.projects-container::-webkit-scrollbar-track {
  background: var(--secondary-bg);
  border-radius: var(--radius-full);
}

.projects-container::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
}

.project-card {
  flex: 0 0 350px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: all var(--transition-base);
  cursor: pointer;
  opacity: 0;
  transform: translateY(50px);
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-xl), var(--glow-md);
}

.project-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.project-card:hover .project-image img {
  transform: scale(1.1);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-base);
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-btn {
  padding: var(--space-3) var(--space-6);
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--radius-full);
  color: var(--text-primary);
  font-family: var(--font-family);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-base);
}

.project-btn:hover {
  transform: scale(1.05);
  box-shadow: var(--glow-md);
}

.project-info {
  padding: var(--space-6);
}

.project-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.project-description {
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
  line-height: 1.6;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.tech-tag {
  padding: var(--space-1) var(--space-3);
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  color: var(--text-accent);
  font-weight: 500;
}

/* ===== CONTACT SECTION ===== */
.contact {
  padding: var(--space-24) 0;
  position: relative;
}

.contact-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-16);
  align-items: start;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.form-group {
  position: relative;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--space-4) var(--space-5);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  transition: all var(--transition-base);
  opacity: 0;
  transform: translateX(-30px);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--text-accent);
  box-shadow: var(--glow-sm);
  background: rgba(255, 255, 255, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: var(--text-muted);
}

.form-group label {
  position: absolute;
  top: -8px;
  left: var(--space-4);
  background: var(--primary-bg);
  padding: 0 var(--space-2);
  font-size: var(--font-size-xs);
  color: var(--text-accent);
  font-weight: 500;
  opacity: 0;
  transform: translateY(10px);
  transition: all var(--transition-base);
}

.form-group input:focus + label,
.form-group textarea:focus + label,
.form-group input:not(:placeholder-shown) + label,
.form-group textarea:not(:placeholder-shown) + label {
  opacity: 1;
  transform: translateY(0);
}

.submit-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  padding: var(--space-4) var(--space-8);
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--radius-full);
  color: var(--text-primary);
  font-family: var(--font-family);
  font-size: var(--font-size-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-lg);
  opacity: 0;
  transform: translateY(20px);
}

.submit-btn:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-xl), var(--glow-lg);
}

.submit-btn i {
  font-size: var(--font-size-lg);
  transition: transform var(--transition-base);
}

.submit-btn:hover i {
  transform: translateX(5px);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
  opacity: 0;
  transform: translateX(30px);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
}

.contact-item i {
  font-size: var(--font-size-xl);
  color: var(--text-accent);
}

.contact-item span {
  color: var(--text-secondary);
  font-weight: 500;
}

.social-links {
  display: flex;
  gap: var(--space-4);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-base);
}

.social-link:hover {
  color: var(--text-accent);
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg), var(--glow-sm);
}

.social-link i {
  font-size: var(--font-size-xl);
}

/* ===== FOOTER ===== */
.footer {
  padding: var(--space-16) 0;
  background: var(--secondary-bg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
}

.footer-links {
  display: flex;
  gap: var(--space-8);
}

.footer-links a {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 400;
  transition: color var(--transition-base);
}

.footer-links a:hover {
  color: var(--text-accent);
}

.footer-social {
  display: flex;
  gap: var(--space-4);
}

.footer-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-base);
}

.footer-social a:hover {
  color: var(--text-accent);
  transform: translateY(-2px);
  box-shadow: var(--glow-sm);
}

.footer-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--text-accent);
  border-radius: 50%;
  opacity: 0.6;
}

.particle-1 {
  top: 20%;
  left: 10%;
  animation: float 8s ease-in-out infinite;
}

.particle-2 {
  top: 60%;
  left: 30%;
  animation: float 6s ease-in-out infinite reverse;
}

.particle-3 {
  top: 40%;
  left: 70%;
  animation: float 10s ease-in-out infinite;
}

.particle-4 {
  top: 80%;
  left: 50%;
  animation: float 7s ease-in-out infinite reverse;
}

.particle-5 {
  top: 30%;
  left: 90%;
  animation: float 9s ease-in-out infinite;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .hero-title {
    font-size: var(--font-size-5xl);
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
    text-align: center;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }

  .projects-container {
    padding: var(--space-4);
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-4);
  }

  .section-title {
    font-size: var(--font-size-3xl);
  }

  .nav-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100vh;
    background: var(--primary-bg);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: var(--space-8);
    transition: right var(--transition-base);
    z-index: 999;
  }

  .nav-menu.active {
    right: 0;
  }

  .nav-hamburger {
    display: flex;
  }

  .hero {
    padding: 0 var(--space-4);
  }

  .hero-title {
    font-size: var(--font-size-4xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
  }

  .hero-spline {
    right: -20%;
    width: 80%;
    opacity: 0.5;
  }

  .image-container {
    width: 250px;
    height: 250px;
  }

  .skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: var(--space-3);
  }

  .projects-container {
    flex-direction: column;
    overflow-x: visible;
  }

  .project-card {
    flex: none;
    width: 100%;
  }

  .footer-content {
    flex-direction: column;
    gap: var(--space-6);
    text-align: center;
  }

  .footer-links {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .cta-button {
    padding: var(--space-4) var(--space-6);
    font-size: var(--font-size-base);
  }

  .about-description {
    font-size: var(--font-size-base);
  }

  .skills-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .contact-form {
    gap: var(--space-4);
  }

  .form-group input,
  .form-group textarea {
    padding: var(--space-3) var(--space-4);
  }

  .submit-btn {
    padding: var(--space-3) var(--space-6);
    font-size: var(--font-size-base);
  }
}

/* ===== SCROLL ANIMATIONS ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.8s ease-out;
}

.slide-in-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-in-right {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.8s ease-out;
}

.slide-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}

.scale-in {
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.8s ease-out;
}

.scale-in.visible {
  opacity: 1;
  transform: scale(1);
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
/* GPU acceleration for animations */
.hero-spline,
.floating-orbs,
.orb,
.bg-orb,
.project-card,
.skill-item,
.cta-button,
.submit-btn,
.social-link {
  will-change: transform;
  transform: translateZ(0);
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .float,
  .glow-pulse {
    animation: none;
  }
}

/* Print styles */
@media print {
  .preloader,
  .nav,
  .floating-orbs,
  .bg-elements,
  .footer-particles {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  .hero,
  .about,
  .projects,
  .contact {
    page-break-inside: avoid;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #ffffff;
    --text-secondary: #ffffff;
    --text-muted: #cccccc;
    --primary-blue: #00ffff;
    --accent-violet: #ff00ff;
  }

  .glass {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid white;
  }
}

/* Focus management for accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-blue);
  color: var(--primary-bg);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
}

.skip-link:focus {
  top: 6px;
}

/* Ensure interactive elements are large enough on touch devices */
@media (pointer: coarse) {
  .nav-link,
  .cta-button,
  .project-btn,
  .submit-btn,
  .social-link {
    min-height: 44px;
    min-width: 44px;
  }
}

/* ===== CUSTOM CURSOR (Desktop only) ===== */
@media (pointer: fine) {
  body {
    cursor: none;
  }

  .cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    background: var(--primary-blue);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: difference;
    transition: transform 0.1s ease-out;
  }

  .cursor-follower {
    position: fixed;
    width: 40px;
    height: 40px;
    border: 2px solid rgba(0, 212, 255, 0.3);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
    transition: all 0.3s ease-out;
  }

  .cursor.hover {
    transform: scale(1.5);
    background: var(--neon-purple);
  }

  .cursor-follower.hover {
    transform: scale(1.5);
    border-color: rgba(168, 85, 247, 0.5);
  }
}

/* ===== LOADING STATES ===== */
.loading {
  opacity: 0.5;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid var(--primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== FALLBACK STYLES ===== */
.no-animations * {
  animation: none !important;
  transition: none !important;
}

.no-animations .preloader {
  display: none !important;
}

.no-animations .main {
  opacity: 1 !important;
}

/* ===== SUCCESS STATES ===== */
.form-success {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
  color: #22c55e;
}

.form-error {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

/* ===== FINAL POLISH ===== */
::selection {
  background: var(--primary-blue);
  color: var(--primary-bg);
}

::-moz-selection {
  background: var(--primary-blue);
  color: var(--primary-bg);
}

/* Smooth focus outline */
*:focus-visible {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Hide scrollbar but keep functionality */
.projects-container::-webkit-scrollbar {
  height: 6px;
}

.projects-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.projects-container::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 3px;
}

.projects-container::-webkit-scrollbar-thumb:hover {
  background: var(--primary-blue);
}
